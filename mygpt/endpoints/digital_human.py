import json
import time
import uuid
from typing import AsyncIterator

import async<PERSON>
from fastapi import APIRouter, Body, Depends, HTTPException, Request, Query
from fastapi.responses import JSONResponse, StreamingResponse
from langchain_core.messages import convert_to_messages
from loguru import logger as logging
from openai import (
    APIConnectionError,
    APIStatusError,
    AsyncAzureOpenAI,
    AsyncOpenAI,
    AsyncStream,
    RateLimitError,
)
from openai.types.chat import Chat<PERSON>ompletionChunk
from skywalking.decorators import trace
from starlette.status import HTTP_204_NO_CONTENT
from tortoise.expressions import Q
from tortoise.functions import Count
from tortoise.transactions import in_transaction

from mygpt import settings
from mygpt.agent_exp.service_handler import (
    pre_question,
    format_open_ai_response,
    format_open_ai_chunk_response,
)
from mygpt.agent_functioncall.callback_handler import (
    DigitalHumanFCAgentStreamCallback<PERSON>andler,
    FCAgentBaseCallbackHandler,
)
from mygpt.agent_functioncall.functioncall_agent_engine import AgentFunction<PERSON>allEngine
from mygpt.agent_functioncall.schemas.prompt_schemas import PromptType
from mygpt.auth0.init import auth
from mygpt.authorization import (
    current_user,
    verify_admin_access,
    depend_api_key_with_none,
)
from mygpt.endpoints.questions import save_message_question
from mygpt.enums import (
    AIConfigType,
    AIStatus,
    OpenAIModel,
    StreamingMessageDataFormat,
)
from mygpt.error import NotFoundException, UnauthorizedException
from mygpt.models import Robot, RobotConfig, RobotPrompt, User, Session
from mygpt.openai_utils import get_chat_history_turbo, RedisChatMessageHistory
from mygpt.prompt import digital_human_default_prompt
from mygpt.schemata import (
    DigitalHumanRobotIn,
    DigitalHumanRobotOut,
    QuestionIn,
    ApiKey,
)
from mygpt.services.quota_service import QuotaService, QuotaException
from mygpt.utils import num_tokens_from_string
from mygpt.utils_stripe import (
    verify_questions,
)

router = APIRouter(prefix="/digitals", tags=["Digital Human"])
api_router = APIRouter(prefix="/v1/digitals", tags=["Digital Human"])

TURBO_35_16K_MAX_TOKENS = 16385


@trace()
def check_max_token(prompt: str, context: str, completion_token: int = 500):
    """
    检查prompt和context的token数量是否超过限制
    """
    prompt_tokens = num_tokens_from_string(prompt)
    context_tokens = num_tokens_from_string(context)
    max_tokens = TURBO_35_16K_MAX_TOKENS - completion_token
    if prompt_tokens + context_tokens >= max_tokens:
        logging.warning(
            f"【Digital】This model's maximum context length is {max_tokens} tokens. \
However, your provided context and prompt are {prompt_tokens + context_tokens} tokens."
        )
        raise HTTPException(
            status_code=400,
            detail=f"【Digital】This model's maximum context length is {max_tokens} tokens. \
However, your provided context and prompt are {prompt_tokens + context_tokens} tokens.",
        )


@router.post(
    "",
    response_model=DigitalHumanRobotOut,
    dependencies=[
        Depends(auth.implicit_scheme),
    ],
)
async def create_digital_ai(
    robot: DigitalHumanRobotIn, user: User = Depends(current_user)
):
    # Check if user can create a new bot based on their quota
    try:
        await QuotaService.check_bot_quota(user.id)
    except QuotaException as e:
        # Handle the quota error with proper status code
        raise HTTPException(status_code=e.status_code, detail=e.message)

    async with in_transaction("default") as conn:
        robot_obj = await Robot.create(
            using_db=conn,
            user_id=user.user_id,
            ai_status=AIStatus.READY,
            name=robot.name,
            discrible=robot.discrible,
            ai_model=robot.ai_model,
        )
        await RobotPrompt.create(
            using_db=conn,
            robot_id=robot_obj.id,
            prompt=None,
        )
        robot_obj = await Robot.get(
            id=robot_obj.id,
            deleted_at__isnull=True,
        ).prefetch_related("robotprompts", "robotconfigs")
        return await DigitalHumanRobotOut.from_tortoise_orm(robot_obj)


@router.get(
    "/{ai_id}",
    response_model=DigitalHumanRobotOut,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
)
async def get_digital_ai(ai_id: str):
    robot_obj = await Robot.get(
        id=ai_id,
        deleted_at__isnull=True,
    ).prefetch_related("robotprompts", "robotconfigs")

    return await DigitalHumanRobotOut.from_tortoise_orm(robot_obj)


@router.post(
    "/{ai_id}/context",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
)
async def update_digital_ai_context(
    ai_id: str,
    context: str = Body(
        ...,
        embed=True,
    ),
):
    prompt_obj = await RobotPrompt.get_or_none(
        robot_id=ai_id,
        deleted_at__isnull=True,
    )
    if prompt_obj:
        prompt = prompt_obj.prompt or digital_human_default_prompt
    else:
        prompt = digital_human_default_prompt
    check_max_token(prompt, context)

    ctx_obj = await RobotConfig.get_or_none(
        robot_id=ai_id,
        key=AIConfigType.ENV_CONTEXT,
    )
    if ctx_obj:
        ctx_obj.value = context
        await ctx_obj.save()
    else:
        await RobotConfig.create(
            robot_id=ai_id,
            key=AIConfigType.ENV_CONTEXT,
            value=context,
        )


@router.post(
    "/{ai_id}/prompt",
    status_code=HTTP_204_NO_CONTENT,
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
)
async def update_digital_ai_prompt(
    ai_id: str,
    prompt: str = Body(
        ...,
        embed=True,
    ),
):
    if prompt == "":
        prompt = digital_human_default_prompt
    context = await RobotConfig.get_config(ai_id, AIConfigType.ENV_CONTEXT) or ""
    check_max_token(prompt, context)
    robot_prompt = await RobotPrompt.get_or_none(
        robot_id=ai_id,
        deleted_at__isnull=True,
    )
    if not robot_prompt:
        robot_prompt = await RobotPrompt.create(
            robot_id=ai_id,
            prompt=prompt,
        )
    else:
        robot_prompt.prompt = prompt
        await robot_prompt.save()


async def stream_response(
    stream: AsyncStream[ChatCompletionChunk],
    start_time: float,
) -> AsyncIterator[str]:
    """
    调用openai chat api后，返回的是一个stream，这个函数用于将stream进行流式返回
    同时记录第一个字符返回的时间
    """
    got_fisrt_char = False
    async for chunk in stream:
        if not got_fisrt_char:
            got_fisrt_char = True
            logging.info(
                f"【Digital】Got first char in {time.time() - start_time} seconds"
            )
        yield chunk.json()


def get_openai_client() -> AsyncOpenAI:
    return AsyncOpenAI(
        api_key=settings.OPENAI_API_KEY,
    )


def get_azure_openai_client() -> AsyncAzureOpenAI:
    azure_model = settings.AZURE_MODEL.get(OpenAIModel.GPT_35_TURBO)
    return AsyncAzureOpenAI(
        api_key=azure_model.identity_key,
        api_version=azure_model.version,
        azure_endpoint=azure_model.endpoints,
        azure_deployment=azure_model.deployment_name,
    )


async def prepare_chat_messages(
    messages: list,
    robot_id: str,
):
    prompt_obj = await RobotPrompt.get_or_none(
        robot_id=robot_id,
        deleted_at__isnull=True,
    )
    context = await RobotConfig.get_config(robot_id, AIConfigType.ENV_CONTEXT) or ""
    if prompt_obj:
        # 使用配置的prompt，如果没有配置，则使用默认的prompt
        prompt = prompt_obj.prompt or digital_human_default_prompt
    else:
        prompt = digital_human_default_prompt

    # 将context放入系统prompt种，并组成最终的messages
    system_prompt = prompt.format(
        context=context,
    )
    chat_messages = [{"role": "system", "content": system_prompt}]
    chat_messages.extend(messages)
    return chat_messages


# @router.post(
#     "/{ai_id}/chat",
#     dependencies=[
#         Depends(auth.implicit_scheme),
#         Depends(verify_admin_access),
#     ],
# )
# @api_router.post(
#     "/{ai_id}/chat",
#     dependencies=[
#         Depends(auth.implicit_scheme),
#         Depends(verify_admin_access),
#     ],
# )
async def chat(
    ai_id: str,
    params: dict = Body(..., description="Json body. Same with openai chat api."),
    user: User = Depends(current_user),
):
    start_time = time.time()
    required_key = ["messages"]
    for key in required_key:
        if key not in params:
            logging.info(params)
            raise HTTPException(status_code=400, detail=f"Missing required key '{key}'")
    # 处理chat的message
    messages = params.pop("messages")
    chat_messages = await prepare_chat_messages(messages, ai_id)

    # 获取openai模型
    model = OpenAIModel.GPT_4_OMNI
    if "model" in params:
        params.pop("model")
    if "max_tokens" in params:
        params.pop("max_tokens")
    if settings.OPENAI_JUST_AZURE:
        client = get_azure_openai_client()
    else:
        client = get_openai_client()
    stream = params.get("stream", False)

    # 调用openai chat api
    try:
        rsp = await client.chat.completions.create(
            model=model,
            max_tokens=500,
            messages=chat_messages,
            timeout=60 if stream else 10,
            **params,
        )
    except APIConnectionError as e:
        logging.error(e.message)
        return HTTPException(
            status_code=500,
            detail=e.message,
        )
    except (APIStatusError, RateLimitError) as e:
        return JSONResponse(e.response.json(), status_code=e.status_code)
    except Exception as e:
        logging.error(e)
        return HTTPException(
            status_code=500,
            detail="【Digital】" + e.message,
        )
    finally:
        logging.info(f"【Digital】Got response in {time.time() - start_time} seconds")
    return StreamingResponse(stream_response(rsp, start_time)) if stream else rsp


async def stream_response_agent(
    stream: AsyncStream[ChatCompletionChunk],
    start_time: float,
) -> AsyncIterator[str]:
    """
    调用openai chat api后，返回的是一个stream，这个函数用于将stream进行流式返回
    同时记录第一个字符返回的时间
    """
    got_fisrt_char = False
    async for chunk in stream:
        if not got_fisrt_char:
            got_fisrt_char = True
            logging.info(
                f"【Digital】Got first char in {time.time() - start_time} seconds"
            )
        yield chunk.json()


async def prepare_answer2oaichunk(reply: str, message_id) -> AsyncIterator:
    """
    将reply转换成chunk
    """
    for char in reply:
        chunk = format_open_ai_chunk_response(char, message_id)
        yield chunk
    chunk = format_open_ai_chunk_response("", message_id, finish_reason="stop")
    yield chunk


from langchain_core.messages.ai import AIMessageChunk


async def prepare_chunk2oaichunk(reply: AsyncIterator, message_id) -> AsyncIterator:
    """
    将reply转换成chunk
    """
    async for item in reply:
        if isinstance(item, AIMessageChunk):
            chunk = format_open_ai_chunk_response(item.content, message_id)
        else:
            chunk = format_open_ai_chunk_response(item, message_id)
        yield chunk
    chunk = format_open_ai_chunk_response("", message_id, finish_reason="stop")
    yield chunk


@router.get(
    "/{ai_id}/welcome",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
)
@api_router.get(
    "/{ai_id}/welcome",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
)
async def welcome_message(ai_id: str):
    logging.info(f"【Agent Digital Human Welcome message】start, ai_id: {ai_id}")
    robot_obj = (
        await Robot.get_or_none(id=ai_id, deleted_at__isnull=True)
        .prefetch_related("robotconfigs")
        .annotate(
            file_count=Count(
                "vectorfiles", _filter=Q(vectorfiles__deleted_at__isnull=True)
            ),
        )
    )
    logging.info("【Agent Digital Human Welcome message】robot_obj: %s", robot_obj)
    if not robot_obj:
        logging.error(
            f"【Agent Digital Human Welcome message】AI not exist. ai_id: {ai_id}"
        )
        raise NotFoundException("AI not exist.")
    if not robot_obj.subject_name:
        robot_obj.subject_name = robot_obj.name
    # 输出需要整理成为OpenAI的格式
    robot_configs = robot_obj.robotconfigs.related_objects
    logging.info(
        f"【Agent Digital Human Welcome message】robot_configs: {robot_configs}"
    )
    welcome_message = ""
    for config in robot_configs:
        if config.key == AIConfigType.WIDGET_CONFIGS:
            widget_configs = json.loads(config.value)
            chatbot = widget_configs.get("chatbot")
            if chatbot:
                welcomeMessage = chatbot.get("welcomeMessage")
                if welcomeMessage:
                    if welcomeMessage.get("checked"):
                        welcome_message = welcomeMessage.get("value")
    logging.info(
        f"【Agent Digital Human Welcome message】welcome_message: {welcome_message}"
    )
    resp = format_open_ai_response(welcome_message, message_id=uuid.uuid4())
    logging.info(f"【Agent Digital Human Welcome message】resp: {resp.json()}")
    return resp


async def manage_digital_human_session_id(session_id: str):
    client = RedisChatMessageHistory(key_prefix="digital_human:", ttl=30)
    stamp_session_id = await client.get_stamp_session_id(session_id)
    if not stamp_session_id:
        # 秒级时间戳
        stamp = str(int(time.time()))
        stamp_session_id = f"{session_id}_{stamp}"
        await client.add_stamp_session_id(session_id, stamp_session_id)
    else:
        # 更新相同的值, 延长redis中的存活时间
        await client.add_stamp_session_id(session_id, stamp_session_id)
    return stamp_session_id


# @router.post(
#     "/{ai_id}/{session_id}/chat_agent",
#     dependencies=[
#         Depends(auth.implicit_scheme),
#         Depends(verify_admin_access),
#     ],
# )
# @api_router.post(
#     "/{ai_id}/{session_id}/chat_agent",
#     dependencies=[
#         Depends(auth.implicit_scheme),
#         Depends(verify_admin_access),
#     ],
# )
@router.post(
    "/{ai_id}/chat",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
)
@api_router.post(
    "/{ai_id}/chat",
    dependencies=[
        Depends(auth.implicit_scheme),
        Depends(verify_admin_access),
    ],
)
async def chat_agent(
    ai_id: uuid.UUID,
    # session_id: str,
    request: Request,
    params: dict = Body(..., description="Json body. Same with openai chat api."),
    ai_obj: Robot = Depends(verify_questions),
    user: User = Depends(current_user),
    api_key: ApiKey = Depends(depend_api_key_with_none),
):
    start_time = time.time()
    logging.info(f"【Agent Digital Human】Start chat_agent")
    logging.info(f"【Agent Digital Human】params: {params}")
    # 在Redis中获取session_id

    # 处理session_id, 声网呼入一般是传入电话号作为session_id, 但是一个电话可能使用不同机器人, 这里需要使用robot_id+session_id作为唯一标识
    # session_id = f"{ai_id}|{session_id}"  # todo - 暂时写死一个session_id - 目前声网没有切换接口, 我们现在先需要使用原来的接口形式来验证一下新的逻辑
    session_id = "1234567890test5"
    session_id = await manage_digital_human_session_id(session_id)
    # 获取一个时间
    try:
        session = await Session.filter(session_id=session_id)
        if session and not any(s.robot_id == ai_id for s in session):
            # 400 bad request
            raise HTTPException(
                status_code=400,
                detail=f"session_id {session_id} used by other bot, please choose another session_id",
            )
        logging.info(f"【Agent Digital Human】session_id: {session_id}")
        # 获取用户信息
        user_id = api_key.user_id if api_key else None
        if not user_id and user:
            # user_id = user.id
            user_id = user.user_id
        logging.info(f"【Agent Digital Human】user_id: {user_id}")
        # 参数检查 - 数字人的chat接口使用OpenAI Style的参数形式
        required_key = ["messages"]
        for key in required_key:
            # 检查是否有必要的key, 按照OpenAI的API接口标准，messages是必要的
            if key not in params:
                logging.error(f"【Agent Digital Human】Missing required key '{key}'")
                raise HTTPException(
                    status_code=400, detail=f"Missing required key '{key}'"
                )
        user_message = params["messages"][-1]
        if user_message.get("role") != "user":
            user_message = params["messages"][-2]
            assert user_message.get("role") == "user" and user_message.get("content")
        role = user_message.get("role")
        logging.info(f"【Agent Digital Human】user_message: {user_message}")
        if role != "user":
            # 最后一条消息必须是用户消息
            logging.error(
                f"【Agent Digital Human】The last message must be user message. but got {role}"
            )
            return HTTPException(
                status_code=400,
                detail="The last message must be user message",
            )
        # 提取用户消息内容 - 由于历史消息由GBase统一管理, 所以这里我们只需要提取最后一条消息
        question: str = user_message.get("content").strip()
        if not question:
            # 用户消息内容不能为空
            logging.error(
                f"【Agent Digital Human】The user message content cannot be empty"
            )
            return HTTPException(
                status_code=400,
                detail="The user message content cannot be empty",
            )
        logging.info(f"【Agent Digital Human】user question: {question}")
        if "model" in params:
            params.pop("model")
        if "max_tokens" in params:
            params.pop("max_tokens")
        stream = params.get("stream", False)
        logging.info(f"【Agent Digital Human】response stream: {stream}")
        # ----------------- 补全环境信息 -----------------
        # 构建QuestionIn对象
        question_in = QuestionIn(
            question=question,
            session_id=session_id,
            message_id=uuid.uuid4(),
            use_faq=False,
            stream=stream,
            with_images=False,
        )
        created_ip = request.client.host
        if request and "x-real-ip" in request.headers:
            created_ip = request.headers["x-real-ip"]
        created_by = user_id
        question_record_obj, chat_history_str = await pre_question(
            question_in, ai_obj, user_id, created_ip, created_by
        )
    except Exception as e:
        logging.error(e)
        raise e
    # 启动异步线程待等event，用于apm统计接收到第1个字符或完整消息的时间，由stream字段决定 (沿用question接口的逻辑, 备用, 暂时未使用)
    event = asyncio.Event()
    # 调用Agent回答问题
    try:
        history, count = await get_chat_history_turbo(question_in.session_id, 12)
        stream_callback = DigitalHumanFCAgentStreamCallbackHandler(
            question_in=question_in,
            robot=ai_obj,
            format=StreamingMessageDataFormat.OPENAI_AST,
            event=event,
        )
        afc_engine = AgentFunctionCallEngine(
            question_in=question_in,
            robot=ai_obj,
            chat_history=history,
            event=event,
            stream_callback=stream_callback,
            base_model=OpenAIModel.CLAUDE_35_SONNET,
            stream=stream,
            mode="tts",
        )
        response = await afc_engine.run()
        if stream:
            resp = response.gpt_response_iter
            return StreamingResponse(
                resp,
                media_type="text/event-stream",
            )
        else:
            resp = response.content
            resp = format_open_ai_response(resp, question_in.message_id)
        return resp
    except Exception as e:
        logging.error(e)
        return HTTPException(status_code=500, detail="【Agent Digital Human】" + str(e))
    finally:
        pass
    # try:
    #     function_call_sta, reply, operations, function_call_infos = (
    #         await run_function_call_agent(
    #             request,
    #             question_in=question_in,
    #             ai_obj=ai_obj,
    #             chat_history_str=chat_history_str,
    #             user_id=user_id,
    #             event=event,
    #             start_time=start_time,
    #         )
    #     )
    #     logging.info(f"【Agent Digital Human】function_call_sta: {function_call_sta}")
    #     logging.info(f"【Agent Digital Human】reply: {reply}")
    # except Exception as e:
    #     logging.error(e)
    #     return HTTPException(status_code=500, detail="【Agent Digital Human】" + str(e))
    # finally:
    #     logging.info(
    #         f"【Agent Digital Human】Got response in {time.time() - start_time} seconds"
    #     )
    #     # 保存用户的历史信息到关系型数据库
    #     asyncio.create_task(
    #         save_session_title(question_in.session_id, question_record_obj)
    #     )
    #     asyncio.create_task(incr_user_and_robot_question_usage_count(ai_obj))
    #     if (
    #         not question_in.stream
    #     ) and question_record_obj.comes_from != MESSAGE_COMES_FROM.FAQ:
    #         # question_record_obj.status = SessionMessageStatus.FINISHED
    #         # await SessionEvaluate.add_message_count(question_record_obj.session_id)
    #         asyncio.create_task(
    #             save_to_quality_assessment_by_message_id(question_record_obj.id)
    #         )
    #         logging.info("not stream, save question_record_obj, add message count")
    #     await question_record_obj.save()
    #     interval_seconds = time.time() - start_time
    #     logging.info(
    #         f"Stream task create after send request: {interval_seconds} seconds"
    #     )
    # # 返回回答结果
    # if stream:
    #     if isinstance(reply, str):
    #         # 回复信息如果是字符串, 需要对response的形式机型封装
    #         resp = prepare_answer2oaichunk(reply, message_id=question_in.message_id)
    #     else:
    #         # reply
    #         resp = prepare_chunk2oaichunk(reply, message_id=question_in.message_id)
    #     resp = StreamingResponse(resp)
    # else:
    #     if isinstance(reply, str):
    #         # 回复信息如果是字符串, 需要对response的形式机型封装
    #         resp = format_open_ai_response(reply, question_in.message_id)
    #     else:
    #         # stream为False时, reply是AIMessage对象
    #         resp = format_open_ai_response(reply.content, question_in.message_id)
    # return resp


@router.post(
    "/v1/chat/completions",
)
@api_router.post(
    "/v1/chat/completions",
)
async def chat_agent_runtime(
    request: Request,
    ai_id: uuid.UUID = Query(..., alias="bot_id"),
    session_id: str = Query(..., alias="session_id"),
    params: dict = Body(..., description="Json body. Same with openai chat api."),
    # ai_obj: Robot = Depends(verify_questions),
    # user: User = Depends(current_user),
    # api_key: ApiKey = Depends(depend_api_key_with_none),
):
    # 使用ai_id获取Robot对象以及用户信息
    ai_obj = await Robot.get_or_none(
        id=ai_id, deleted_at__isnull=True
    ).prefetch_related(
        "robotconfigs",
        "dictionary",
        "apis",
        "datasets",
        "datasets__vectorfiles",
        "user",
    )
    # 获取用户信息
    user = await ai_obj.user
    user_id = user.user_id
    logging.info(f"【chat_agent_runtime】user_id: {user_id}")
    # 添加访问控制检查
    enable_access_control_value = ai_obj.get_config(AIConfigType.ENABLE_ACCESS_CONTROL)
    enable_access_control = (
        enable_access_control_value.lower() == "true"
        if enable_access_control_value
        else False
    )
    if enable_access_control:
        # 不需要检查用户是否为bot admin或协助者, 只需要检查是否开启了访问控制
        if not user_id:
            raise UnauthorizedException(
                "Access Denied: You must be logged in to access this API."
            )
    # 参数检查 - 数字人的chat接口使用OpenAI Style的参数形式
    required_key = ["messages"]
    for key in required_key:
        # 检查是否有必要的key, 按照OpenAI的API接口标准，messages是必要的
        if key not in params:
            logging.error(f"【chat_agent_runtime】Missing required key '{key}'")
            raise HTTPException(status_code=400, detail=f"Missing required key '{key}'")

    # system_message = None
    # messages = []
    # for message in params["messages"]:
    #     if message.get("role") == "system":
    #         system_message = message
    #         # 如果有system消息, content不能为空
    #         if not message.get("content").strip():
    #             logging.error(
    #                 f"【chat_agent_runtime】The system message content cannot be empty"
    #             )
    #             return HTTPException(
    #                 status_code=400,
    #                 detail="The system message content cannot be empty",
    #             )
    #     else:
    #         messages.append(message)
    # if not messages:
    #     logging.error(f"【chat_agent_runtime】messages is empty")
    #     raise HTTPException(
    #         status_code=400,
    #         # messages中除了system消息外, 必须有用户消息
    #         detail=f"messages is empty, messages must have user message",
    #     )
    # # 容错处理, 存在system消息, 则将Robot中的prompt替换为system消息. 另外确保messages的最后一条消息是用户消息
    # if system_message:
    #     ai_obj.prompt = system_message.get("content")
    messages = params.get("messages")
    # 容错处理, 并提取最后一条的用户消息
    if messages[-1].get("role") != "user":
        # 容错处理, 如果最后一条消息不是用户消息且内容为空, 则删除最后一条消息
        if messages[-1].get("content").strip() == "":
            messages.pop()
    if messages[-1].get("role") != "user":
        # 最后一条消息必须是用户消息
        raise HTTPException(
            status_code=400,
            detail="The last message must be user message",
        )
    # 提取用户消息内容
    question: str = messages[-1].get("content").strip()
    if not question.strip():
        # 用户消息内容不能为空
        logging.error(f"【chat_agent_runtime】The user message content cannot be empty")
        return HTTPException(
            status_code=400,
            detail="The user message content cannot be empty",
        )
    logging.info(f"【chat_agent_runtime】user question: {question}")
    base_model = ai_obj.get_config(AIConfigType.OPENAI_MODEL)
    base_model = OpenAIModel.to_model(base_model)
    if not base_model:
        base_model = OpenAIModel.CLAUDE_35_SONNET
    if "model" in params:
        model = params.pop("model")
        # base_model = OpenAIModel.to_model(model)
    if "max_tokens" in params:
        params.pop("max_tokens")
    # todo - 目前先暂时只支持stream模式
    # stream = params.get("stream", False)
    stream = True
    logging.info(f"【chat_agent_runtime】response stream: {stream}")
    # ----------------- 补全环境信息 -----------------
    message_id = uuid.uuid4()
    # session_id = str(ai_id) + "|" + "chat_agent_runtime_session"
    question_record_obj = await save_message_question(
        ai_obj=ai_obj,
        session_id=session_id,
        user_identifier="",
        user_id=user_id,
        anonymous_username="",
        question=question,
        message_id=message_id,
    )
    # 构建QuestionIn对象
    question_in = QuestionIn(
        question=question,
        session_id=session_id,
        message_id=message_id,
        use_faq=False,
        stream=stream,
        with_images=False,
    )
    created_ip = request.client.host
    if request and "x-real-ip" in request.headers:
        created_ip = request.headers["x-real-ip"]
    created_by = user_id
    # question_record_obj, chat_history_str = await pre_question(
    #     question_in, ai_obj, user_id, created_ip, created_by
    # )
    try:
        # 启动异步线程待等event，用于apm统计接收到第1个字符或完整消息的时间，由stream字段决定 (沿用question接口的逻辑, 备用, 暂时未使用)
        event = asyncio.Event()
        # 调用Agent回答问题
        history, count = await get_chat_history_turbo(question_in.session_id, 20)
        langchain_messages = convert_to_messages(history)
        stream_callback = DigitalHumanFCAgentStreamCallbackHandler(
            question_in=question_in,
            robot=ai_obj,
            format=StreamingMessageDataFormat.OPENAI_AST,
            event=event,
        )
        # messages是List[Dict],标准的OpenAI Style, 但是需要转换成langchain的AIMessage对象
        # langchain_messages = messages_from_dict(messages)
        # langchain_messages = convert_to_messages(messages)
        # logging.info(f"【chat_agent_runtime】langchain_messages: {langchain_messages}")

        storage_callback = FCAgentBaseCallbackHandler(
            question_in=question_in,
            robot=ai_obj,
            history=langchain_messages,
            question_record_obj=question_record_obj,
            event=event,
            store_message=True,
        )
        logging.info(f"【chat_agent_runtime】storage_callback: {storage_callback}")
        afc_engine = AgentFunctionCallEngine(
            question_in=question_in,
            robot=ai_obj,
            chat_history=langchain_messages,
            event=event,
            stream_callback=stream_callback,
            storage_callback=storage_callback,
            base_model=base_model,
            stream=stream,
            prompt_type=PromptType.TTS,
            question_record_obj=question_record_obj,
        )
        logging.info(
            f"【chat_agent_runtime】create afc_engine - afc_engine: {afc_engine}"
        )
        response = await afc_engine.run()
        if stream:
            resp = response.gpt_response_iter
            logging.info(f"【chat_agent_runtime】response stream: {resp}")
            return StreamingResponse(
                resp,
                media_type="text/event-stream",
            )
        else:
            resp = response.content
            resp = format_open_ai_response(resp, question_in.message_id)
        return resp
    except Exception as e:
        logging.error(e)
        return HTTPException(status_code=500, detail="【Agent Digital Human】" + str(e))
