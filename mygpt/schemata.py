import uuid
from datetime import datetime
from decimal import Decimal
from typing import Any, List, Optional, Union, Dict, TYPE_CHECKING

# 避免循环导入
if TYPE_CHECKING:
    from mygpt.models import User
from enum import Enum
from typing import Any, List, Optional, Union, Dict
from uuid import UUID
from enum import Enum

from pydantic import AnyHttpUrl, BaseModel, Field, validator
from tortoise import Tortoise
from tortoise.contrib.pydantic import pydantic_model_creator

from mygpt import models
from mygpt.enums import (
    EMBEDDINGS_MODEL,
    FAQ_TYPE,
    FaqSourceType,
    AIModel,
    AIType,
    OpenAIModel,
    StreamingMessageDataType,
    StripeModel,
    VectorStorageType,
    StreamingMessageDataFormat,
    RobotType,
    ResourceType,
)
from mygpt.models import (
    Plan,
    DurationUnit,
    AddOn,
    QuotaGrantType,
    PlanType,
)

Tortoise.init_models(["mygpt.models"], "models")


class Embeddings(BaseModel):
    id: str
    text: str
    score: Optional[float] = None
    metadata: Optional[dict]
    vector: Optional[list[float]] = None
    storage_type: Optional[VectorStorageType] = None
    dataset_id: Optional[str] = None  # 实际不是dataset_id，为collection_name


class ApiKey(BaseModel):
    api_key: str
    user_id: str
    is_super_admin: Optional[bool] = False
    user: Optional["User"] = None  # 用于存储关联的User对象

    class Config:
        arbitrary_types_allowed = True


class UpdateOpenaiApiKeyIn(BaseModel):
    in_use: bool = True


# class QuestionIn(BaseModel):
#     question: str = Field(max_length=800)
#     temperature: Optional[float] = Field(default=0.6, ge=0, le=1)
#     max_tokens: Optional[int] = Field(default=400, lt=2048)
#     top_p: Optional[int] = Field(default=1, ge=0, le=1)


class CompletionIn(BaseModel):
    prompt: str = Field(max_length=800)
    temperature: Optional[float] = 0
    max_tokens: Optional[int] = 400
    top_p: Optional[int] = 1
    frequency_penalty: Optional[float] = 0
    presence_penalty: Optional[int] = 0
    with_embeddings: Optional[bool] = False


class CompletionOut(BaseModel):
    success: bool = True

    class Choice(BaseModel):
        text: str
        index: int
        logprobs: Union[int, None] = None
        finish_reason: Optional[str] = None

    id: Optional[str] = None
    object: Optional[str] = None
    created: Optional[int] = 0
    model: Optional[str] = "text-davinci-003"
    choices: list[Choice]
    prompt: Optional[str] = None


class QuestionOut(BaseModel):
    answer: Optional[str | dict | List] = None
    messages: Optional[List] = None
    tool_calls: Optional[List] = None


class QuotaDetails(BaseModel):
    """Schema for quota details"""

    used: int  # Renamed from current
    limit: Optional[int] = None  # None means unlimited
    available: Optional[int] = None  # None means unlimited

    @property
    def is_unlimited(self) -> bool:
        return self.limit is None

    @property
    def percentage_used(self) -> Optional[float]:
        if self.limit is None:
            return None
        if self.limit == 0:
            return 100.0 if self.used > 0 else 0.0  # Updated from self.current
        return (self.used / self.limit) * 100  # Updated from self.current


UserOutBase = pydantic_model_creator(models.User, name="UserBaseOut")

PlanOutBase = pydantic_model_creator(
    Plan,
    exclude=("deleted_at", "subscriptions"),
)


# New model for Addon Subscription Info (Definition + Subscription Dates)
class AddonSubscriptionInfo(BaseModel):
    # Fields from Addon model itself (definition)
    id: UUID  # Addon ID
    name: str
    description: Optional[str] = None
    target_field: str  # The quota type this addon affects
    value: Optional[int] = None  # The value this addon provides for the target_field
    price: Optional[float] = None
    # Fields from AddonSubscription model (subscription specific)
    subscription_id: UUID  # AddonSubscription ID
    start_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None


class UserEffectiveQuotas(BaseModel):
    """Schema for effective quota and status information for a user"""

    bot_quota: QuotaDetails
    message_quota: QuotaDetails
    web_page_quota: QuotaDetails
    multi_modal_parsing_quota: QuotaDetails
    is_corporate: bool


class PlanQuotaDetails(BaseModel):
    """Schema for plan quota details"""

    bot_quota: Optional[QuotaDetails] = None
    message_quota: Optional[QuotaDetails] = None
    web_page_quota: Optional[QuotaDetails] = None
    multi_modal_parsing_quota: Optional[QuotaDetails] = None


# New Fused Models
class PlanInfoWithBreakdown(PlanOutBase):
    contribution: PlanQuotaDetails = Field(alias="usage")


class AddonInfoWithBreakdown(AddonSubscriptionInfo):
    contribution: QuotaDetails = Field(alias="usage")


class QuestionMetadata(BaseModel):
    key: str
    value: Optional[str] = None
    type: Optional[str] = None


class QuestionIn(BaseModel):
    question: str = Field(max_length=65535)
    session_id: str = Field(
        description="Session ID\nIt is recommended to use uuid", max_length=100
    )
    message_id: Union[UUID, None] = None
    anonymous_username: Optional[str] = ""
    use_faq: Optional[bool] = True
    max_tokens: Optional[int] = Field(default=3000)
    stream: Optional[bool] = False
    stream_obj: Optional[bool] = False
    format: Optional[StreamingMessageDataFormat] = StreamingMessageDataFormat.PLAIN_TEXT
    with_source: Optional[bool] = False
    with_images: Optional[bool] = None
    debug_with_messages: Optional[bool] = Field(
        default=False,
        description="Debug only, use `debug_with_messages: true, stream: false` to get prompt messages",
    )
    recommend_faq_id: Union[UUID, None] = None
    created_by: Optional[str] = ""
    page_info: Optional[dict] = None
    is_test: Optional[bool] = False
    metadata: Optional[list[QuestionMetadata]] = []
    prompt_custom_goal: Optional[str] = Field(
        max_length=200, default="", description="custom goal for prompt"
    )
    dynamic_background: Optional[Union[str, list]] = Field(
        default="",
        description="json格式字符串或者字典, 动态背景背景信息, 用于添加到agent的meta prompt中",
    )
    language: Optional[str] = None  # [zh, en, ...]
    agent_mode: Optional[str] = None  # [tts, chat, kiosk, contextual]
    model: Optional[str] = None  # [gpt_4o, claude-35-sonnet, o3_mini]
    nick_name: Optional[str] = (
        None  # 用户昵称，可能是：登录用户的用户名，浏览器 hash，用户手动输入的昵称；前端保存和传入
    )
    browser_fingerprint: Optional[str] = (
        None  # 浏览器指纹：前端生成的唯一hash字符串，用于识别匿名用户
    )


class StreamOut(BaseModel):
    message_id: Optional[UUID] = None
    # message_type: StreamingMessageDataType = None
    message_type: Enum = None
    content: Optional[Any] = None
    use_faq: Optional[bool] = False
    tokens: Optional[int] = None


class EmbeddingsIn(BaseModel):
    input: list[str]


class EmbeddingsOut(BaseModel):
    indexs: list[str]


class AnalysisFileOut(BaseModel):
    input: list[str]


class RobotIn(BaseModel):
    id: Optional[str] = None  # Optional robot ID for idempotent creation
    name: Optional[str] = None
    discrible: Optional[str] = None
    subject_name: Optional[str] = None
    prompt: Optional[str] = None
    url: Optional[str] = None
    dataset_ids: Optional[list[str]] = None
    init_default_datasets: Optional[bool] = False
    ai_type: AIType = AIType.PRIVATE
    ai_model: AIModel = AIModel.FILE
    robot_type: RobotType = RobotType.RAG
    embedding_model_name: Optional[str] = None  # 向量模型
    embedding_dimensions: int = Field(default=None, ge=1, lt=3072)
    agent_function_call_ids: Optional[list[str]] = None


class DigitalHumanRobotIn(BaseModel):
    name: str
    discrible: Optional[str] = None
    ai_type: AIType = AIType.PRIVATE
    ai_model: AIModel = AIModel.DIGITAL_HUMAN


class UpdateRobotIn(BaseModel):
    name: Optional[str]
    discrible: Optional[str] = None
    ai_type: Optional[AIType] = None
    # 机器人类型, RAG / 数字人
    robot_type: Optional[RobotType] = RobotType.RAG
    prompt: Optional[str] = None
    subject_name: str = None
    ai_model: Optional[str] = None
    embedding_model_name: Optional[str] = None
    embedding_dimensions: int = Field(default=None, ge=1, lt=3072)


class QueryEmbeddingsIn(BaseModel):
    prompt: str = Field(max_length=500)
    query_key_words: str
    count: Optional[int] = 3
    score: Optional[float] = 0.82
    opensearch: Optional[bool] = False
    language: Optional[str] = "ja"
    openai_model: str


class IndexInfo(BaseModel):
    id: str
    score: float


class QuestionBatchCreate(BaseModel):
    count: int = Field(gt=1, lt=10, default=3)
    vector_file_id: Optional[str] = None


VectorFileOutBase = pydantic_model_creator(
    models.VectorFile,
    name="VectorFileOutBase",
    exclude=(
        "deleted_at",
        "robot",
        "vectors",
        "robot_id",
        "file_lang",
        "key",
        "metadata",
        "dataset",
        "faq_resources",
        "integration_rules",
        "integrationrule_vectorfiles",
        "lark_file",
    ),
    computed=("tokens",),
    # optional 参数适用简单的标量字段（如 string、integer 等），但对于复杂的关系字段（如 OneToOne、ForeignKey 等），optional无效，可能得使用显式的模型继承和字段重定义的方式。
    # optional=("lark_file",),
)

LarkFileOut = pydantic_model_creator(
    models.LarkFile,
    name="LarkFileOut",
    exclude=(
        "deleted_at",
        "vector_file",
    ),
)


class VectorFileOut(VectorFileOutBase):
    lark_file: Optional[LarkFileOut] = None


VectorFileIn = pydantic_model_creator(
    models.VectorFile,
    name="VectorFileIn",
    exclude=(
        "deleted_at",
        "robot",
        "vectors",
        "robot_id",
        "key",
        "metadata",
        "dataset",
        "content_hash",
        "updated_at",
        "created_at",
        "index_ids",
        "dataset_id",
        "file_type",
        "faq_resources",
        "integration_rules",
        "integrationrule_vectorfiles",
    ),
)

VectorOut = pydantic_model_creator(
    models.Vector,
    name="VectorOut",
    exclude=("vector_file",),
    computed=("text",),
)


class EmbeddingsUrlIn(BaseModel):
    url: AnyHttpUrl
    subject_name: Optional[str] = None
    ai_type: AIType = AIType.PRIVATE
    create_ai: Optional[bool] = True


class EmbeddingsAIUrlIn(BaseModel):
    url: str
    single_crapy: bool = True


class TranslateQuestionIn(BaseModel):
    source_lang: str
    translate_question: bool


class AddLinksIn(BaseModel):
    links: list[str]
    ai_id: Optional[str] = None


class StripeProductApiIdOut(BaseModel):
    order: int
    api_id_monthly: str
    api_id_yearly: str


class MyPlanOut(BaseModel):
    api_id: Optional[str] = None
    sub_id: Optional[str] = None
    type: Optional[str] = None
    created_at: Optional[datetime] = None
    canceled_at: Optional[datetime] = None
    end_at: Optional[datetime] = None
    mode: StripeModel = StripeModel.SUBSCRIPTION
    invoice: Optional[str] = None
    customer_details: Optional[dict] = None

    name: Optional[str] = None
    max_questions: Optional[int] = None
    used_questions: Optional[int] = None
    max_tokens: Optional[int] = None
    used_tokens: Optional[int] = None
    max_upload_file: Optional[int] = None
    param_ext: Optional[dict] = None
    order: Optional[int] = None
    month: Optional[int] = None
    product: Optional[str] = None
    questions_start_date: Optional[str] = None
    questions_end_date: Optional[str] = None


UserOut = pydantic_model_creator(
    models.User,
    name="UserOut",
)


class AddMemberRequest(BaseModel, allow_population_by_field_name=True):
    user_ids: list[str]
    resource_type: ResourceType = ResourceType.ROBOT
    resource_id: str = Field(alias="ai_id")


class UserMemberIn(BaseModel):
    req: list[AddMemberRequest]


UserMemberOut = pydantic_model_creator(
    models.AccountMember,
    name="UserMemberOut",
)


RobotOut = pydantic_model_creator(
    models.Robot,
    name="RobotOut",
    computed=["file_count", "faq_count"],
    exclude=(
        "urls",
        "robotconfigs",
        # 'questions'
    ),
)

DigitalHumanRobotOut = pydantic_model_creator(
    models.Robot,
    name="DigitalHumanRobotOut",
    exclude=(
        "urls",
        "robotconfigs",
        "datasets",
        "questions",
    ),
    computed=["ai_prompt", "context"],
)

RobotPageOut = pydantic_model_creator(
    models.Robot,
    name="RobotPageOut",
    computed=["file_count", "faq_count"],
    exclude=(
        "urls",
        "robotconfigs",
        "questions",
        "robot2agentapisrel",
        "agentfunctioncall",
        "apis",
    ),
)

AIDetailOut = pydantic_model_creator(
    models.Robot,
    name="AIDetailOut",
    computed=["file_count", "configs"],
    exclude=("robotconfigs",),
)

AISimpleOut = pydantic_model_creator(
    models.Robot,
    name="AISimpleOut",
    exclude=("apis", "urls", "robotconfigs", "questions", "datasets"),
)


class AIMemberOut(BaseModel):
    ai: AISimpleOut
    members: Optional[List[UserMemberOut]] = None


QuestionObjOut = pydantic_model_creator(
    models.Question,
    name="QuestionOut",
)

ApiKeyOut = pydantic_model_creator(models.Apikey, name="ApiKeyOut")

UrlCreateOut = pydantic_model_creator(models.Url, name="UrlOut")

OpenaiApiKeyOut = pydantic_model_creator(models.OpenaiApikey, name="OpenaiApiKeyOut")

ImageAttachmentOut = pydantic_model_creator(
    models.ImageAttachment, name="ImageAttachmentOut", exclude=("robot",)
)

StripePaymentsOut = pydantic_model_creator(
    models.StripePayments,
    name="StripePaymentsOut",
)

FaqResourceBaseOut = pydantic_model_creator(
    models.FaqResource,
    name="FaqBaseResourceOut",
    exclude=("created_at", "updated_at", "deleted_at", "faq", "vector_file"),
)


class FaqResourceOut(FaqResourceBaseOut):
    vector_file: Optional[VectorFileOutBase] = None


FaqsBaseOut = pydantic_model_creator(
    models.Faqs,
    name="FaqsBaseOut",
    exclude=("deleted_at", "robot", "user", "dataset"),
)


class FaqsOut(FaqsBaseOut):
    resources: List[FaqResourceOut] = []


class FaqsOutSearch(FaqsOut):
    score: Optional[float] = None


FaqsBaseIn = pydantic_model_creator(
    models.Faqs,
    name="FaqsIn",
    exclude=(
        "id",
        "created_at",
        "updated_at",
        "deleted_at",
    ),
)


class VectorCollectionFilter(BaseModel):
    vector_file_id: UUID
    page_numbers: Optional[List[int]] = None


class FaqsIn(FaqsBaseIn):
    type: Optional[FAQ_TYPE] = FAQ_TYPE.ANSWER
    resources: Optional[List[VectorCollectionFilter]] = None


FaqsConfigIn = pydantic_model_creator(
    models.FaqsConfig,
    name="FaqsConfigIn",
    exclude=("id", "created_at", "updated_at", "deleted_at", "faqs_config"),
)

SessionOut = pydantic_model_creator(
    models.Session,
    name="SessionOut",
)

SessionBaseOut = pydantic_model_creator(
    models.Session,
    name="SessionBaseOut",
    exclude=(
        "created_at",
        "updated_at",
        "deleted_at",
        "sessionmessages",
        "session_evaluate",
        "sessionusers",
        "robot",
    ),
)

SessionMessageOut = pydantic_model_creator(
    models.SessionMessage,
    name="SessionMessageOut",
    exclude=("updated_at", "deleted_at", "session", "user", "contexts"),
)


class SessionMessageExpandOut(SessionMessageOut):
    default_dataset_id: Optional[str] = None
    citation: Optional[List] = None
    session_message_metadata: List = []


class SessionMessageExpandWithSessionOut(SessionMessageExpandOut):
    session: Optional[SessionBaseOut] = None


class SessionMessageExpandWithSessionHistoryOut(SessionMessageExpandWithSessionOut):
    feedback_type: Optional[str] = None
    feedback_details: Optional[List] = None
    feedback_content: Optional[str] = None


class SessionMessageExpandWithSessionHistoryOut(SessionMessageExpandWithSessionOut):
    feedback_type: Optional[str] = None
    feedback_details: Optional[List] = None
    feedback_content: Optional[str] = None


class SessionOutExpand(SessionOut):
    sessionmessages: List[SessionMessageExpandOut] = []


class WebCrawlerCallbackIn(BaseModel):
    # class WebCrawlerPage(BaseModel):
    #     url: str
    #     html: str
    #     urls: list[str]
    #     status_code: int

    action: str
    data: dict


FaqPropertyIn = pydantic_model_creator(
    models.FaqProperty,
    name="FaqPropertyIn",
    exclude=("id", "created_at", "updated_at", "deleted_at", "faqs"),
)


class FaqPropertyAddFaqsIn(BaseModel):
    question: Optional[str] = None
    answer: Optional[str] = None
    sources: Optional[str] = None
    property_info: Optional[list] = None
    excel_id: Optional[str] = None
    citation: Optional[List] = None
    session_message_id: Optional[str] = None
    source_type: Optional[FaqSourceType] = None
    similar_questions: Optional[list] = None
    recommended_questions: Optional[list] = None
    type: Optional[FAQ_TYPE] = FAQ_TYPE.ANSWER
    resources: Optional[List[VectorCollectionFilter]] = None
    is_search: Optional[bool] = True
    hierarchy_parent_id: Optional[str] = None
    hierarchy_level: Optional[int] = 0
    hierarchy_path: Optional[str] = None


FaqPropertyOut = pydantic_model_creator(
    models.FaqProperty,
    name="FaqPropertyOut",
    exclude=("created_at", "updated_at", "deleted_at", "robot"),
)


class FaqSearchIn(FaqPropertyAddFaqsIn):
    score: Optional[float] = None


class FaqPropertyListOut(BaseModel):
    id: uuid.UUID
    title: Optional[str] = None
    type: Optional[str] = None
    description: Optional[str] = None
    robot_id: Optional[uuid.UUID] = None
    dataset_id: Optional[uuid.UUID] = None
    faq_ids: Optional[list] = None

    class Config:
        orm_mode = True


FunctionCallApiIn = pydantic_model_creator(
    models.FunctionCallApi,
    name="FunctionCallApiIn",
    exclude=(
        "id",
        "created_at",
        "updated_at",
        "deleted_at",
    ),
)

FunctionCallApiOut = pydantic_model_creator(
    models.FunctionCallApi,
    name="FunctionCallApiOut",
    exclude=("created_at", "updated_at", "deleted_at", "robot"),
)

_DatasetIn = pydantic_model_creator(
    models.Dataset,
    name="DatasetIn",
    exclude=(
        "id",
        "user_id",
        "metadata",
        "data_type",
        "created_at",
        "updated_at",
        "deleted_at",
        "dataset_robot",
        "data_status",
        "robots",
    ),
)


class DatasetIn(_DatasetIn):
    id: Optional[str] = None
    embedding_model_name: str = Field(
        default=None,
    )
    embedding_dimensions: int = Field(default=None, ge=1, lt=3072)


DatasetOut = pydantic_model_creator(models.Dataset, name="DatasetOut", exclude=())

DatasetSimpleOut = pydantic_model_creator(
    models.Dataset, name="DatasetSimpleOut", include=("id", "name", "description")
)


class DatasetWithUser(models.Dataset):
    class PydanticMeta:
        exclude = tuple(
            filter(lambda x: "user" != x, models.Dataset.PydanticMeta.exclude)
        )


DatasetWithUserOut = pydantic_model_creator(
    DatasetWithUser,
    name="DatasetWithUserOut",
    exclude=("robots", "metadata", "url_rules"),
)


class DatasetMemberOut(BaseModel):
    dataset: DatasetSimpleOut
    members: Optional[List[UserMemberOut]] = None


InsiderPreviewUserOut = pydantic_model_creator(
    models.InsiderPreviewUser,
    name="InsidePreviewUserOut",
    exclude=("deleted_at"),
)


class ContextQueryOptions(BaseModel):
    ai_id: str
    query: str
    response_language: Optional[str] = None
    query_key_words: Optional[str] = None
    embeddings_model: EMBEDDINGS_MODEL = EMBEDDINGS_MODEL.OPENAI
    embedding_model_name: OpenAIModel = OpenAIModel.TEXT_EMBEDDING_ADA_002
    embedding_dimensions: Optional[int] = None
    openai_apikey: str = None
    count: Optional[int] = 8
    score: Optional[float] = 0.7
    vector_size: Optional[int] = 20
    text_size: Optional[int] = 20
    query_faq_answer_with_embeddings: Optional[bool] = False
    openai_model: OpenAIModel = OpenAIModel.GPT_4_1106_PREVIEW
    vector_collection_filter: Optional[List[VectorCollectionFilter]] = None
    enable_opensearch: Optional[bool] = True
    enable_rerank: Optional[bool] = True


class SearchOption(BaseModel):
    dataset_id: str
    collection_name: str = None
    query: str
    analyzer: Optional[str] = None
    vector_storage_type: VectorStorageType
    query_key_words: str
    embeddings_model: EMBEDDINGS_MODEL = EMBEDDINGS_MODEL.OPENAI
    openai_apikey: str = None
    count: Optional[int] = 8
    score: Optional[float] = 0.7
    vector_size: Optional[int] = 80
    text_size: Optional[int] = 40
    query_faq_answer_with_embeddings: Optional[bool] = False
    vector_collection_filter: Optional[List[VectorCollectionFilter]] = None
    enable_opensearch: Optional[bool] = True
    enable_rerank: Optional[bool] = True


SessionMetricsHourlyIn = pydantic_model_creator(
    models.SessionMetricsHourly,
    name="SessionMetricsHourlyIn",
    exclude=(
        "id",
        "created_at",
        "updated_at",
        "deleted_at",
    ),
)

SessionMetricsHourlyOut = pydantic_model_creator(
    models.SessionMetricsHourly,
    name="SessionMetricsHourlyOut",
    exclude=("deleted_at", "robot_id"),
)

SessionMessagePageAccessInfoIn = pydantic_model_creator(
    models.SessionMessagePageAccessInfo,
    name="SessionMessagePageAccessInfoIn",
    exclude=(
        "id",
        "robot_id",
        "session_message_page_access_metrics",
        "created_at",
        "updated_at",
        "deleted_at",
    ),
)

SessionMessagePageAccessInfoOut = pydantic_model_creator(
    models.SessionMessagePageAccessInfo,
    name="SessionMessagePageAccessInfoOut",
    exclude=("deleted_at", "robot_id", "session_message_page_access_metrics"),
)

SessionMessagePageAccessMetricsOut = pydantic_model_creator(
    models.SessionMessagePageAccessMetrics,
    name="SessionMessagePageAccessMetricsOut",
    exclude=("deleted_at"),
)


class SessionMessagePageMetricsWithCountOut(BaseModel):
    message_count: int
    message_proportion: int
    url: str
    title: Optional[str]


class Recommend(BaseModel):
    id: Optional[str] = None
    name: Optional[str] = None
    language: Optional[str] = None


class RecommendIn(BaseModel):
    recommend_list: List[Recommend]


class ClientWebsocketsOut(BaseModel):
    connection_id: str
    client_addr: str
    ai_id: Optional[str] = None
    session_id: Optional[str] = None  # 用于点击跳到会话历史记录
    session_type: Optional[str] = None
    user_id: Optional[str] = None
    email: Optional[str] = None
    page_url: Optional[str] = None
    created_at: Optional[str] = None
    toast: Optional[bool] = True  # 是否弹toast窗口


SignatureStatisticsIn = pydantic_model_creator(
    models.SignatureStatistics,
    name="SignatureStatisticsIn",
    exclude=(
        "id",
        "created_at",
        "updated_at",
        "deleted_at",
    ),
)

SignatureStatisticsOut = pydantic_model_creator(
    models.SignatureStatistics,
    name="SignatureStatisticsOut",
    exclude=("deleted_at",),
)


class RecommendedOut(BaseModel):
    question: str
    stream: Optional[bool] = False
    message_id: Optional[UUID] = None


DictionaryIn = pydantic_model_creator(
    models.Dictionary,
    name="DictionaryIn",
    exclude=(
        "id",
        "robot_id",
        "created_at",
        "updated_at",
    ),
)

DictionaryOut = pydantic_model_creator(
    models.Dictionary,
    name="DictionaryOut",
    exclude=("robot_id",),
)


class SearchResult(BaseModel):
    id: str
    question: str
    score: float

    @classmethod
    def build_from_hit(cls, hit):
        QUERY_FIELDS = ("contents.ja", "contents.zh", "contents.en", "contents.default")
        id, score, question = hit["_id"], hit["_score"], None

        if "highlight" in hit:
            for key in QUERY_FIELDS:
                if key in hit["highlight"]:
                    question = hit["highlight"][key][0]
                    break

        if not question:
            question = hit["_source"]["contents"]["default"]

        return cls(id=hit["_id"], score=hit["_score"], question=question)


class SimilarQuestions(BaseModel):
    question: str
    count: int
    language: Optional[str] = None


class FinalQuestionParams(BaseModel):
    """
    用于生成最终问题prompt的参数

    Args:
        bot_name: str # 机器人名称

        openai_model: OpenAIModel

        unknown_text: Optional[str] = None # 未知回答预设文本

        response_language: Optional[str] = None # 用于指定回复语言

        chat_history: Optional[list] = None # 对话历史

        llm_provider: Optional[str] = None # LLM提供商

        fallback_to_chatgpt: bool = False # 是否回退到chatgpt

        dictionaries: Optional[list] = None # 自定义词典

        talking_style: Optional[str] = 'default'
            对话风格, 默认为default。
            亲切：genial
            幽默：humor
            可爱：lovely
            严谨：rigorous

    """

    bot_name: str
    display_subject_name: str
    openai_model: OpenAIModel
    unknown_text: Optional[str] = None
    response_language: Optional[str] = None  # 用于指定回复语言
    chat_history: Optional[list] = None  # 对话历史
    llm_provider: Optional[str] = None  # LLM提供商
    fallback_to_chatgpt: bool = False  # 是否回退到chatgpt
    dictionaries: Optional[list] = None  # 自定义词典
    talking_style: Optional[str] = "default"


class RobotPVIn(BaseModel):
    url: Optional[str] = None


class CloneProgressOut(BaseModel):
    total: int  # 总数
    done: int  # 已完成
    remaining: int  # 剩余


class DatasetCloneProgressOut(
    pydantic_model_creator(models.Dataset, name="DatasetOut", exclude=())
):
    clone_progress: Union[CloneProgressOut, None] = None


class LarkFile(BaseModel):
    created_time: str
    modified_time: str
    name: str
    owner_id: str
    parent_token: str
    token: str
    type: str
    url: str


class GenPromptIn(BaseModel):
    prompt_in: str
    ai_id: str = None
    cur_prompt: Optional[str] = None
    stream: bool = True
    message_id: str = None


class GenPromptOut(BaseModel):
    prompt: str
    role: str = None
    general: str = None


AgentFunctionCallApiOut = pydantic_model_creator(
    models.AgentFunctionCallApi,
    name="AgentFunctionCallApiOut",
    exclude=("created_at", "updated_at", "deleted_at", "user", "robot"),
)

AgentFunctionCallApiIn = pydantic_model_creator(
    models.AgentFunctionCallApi,
    name="AgentFunctionCallApiIn",
    exclude=(
        "id",
        "user",
        "created_at",
        "updated_at",
        "deleted_at",
        "robots_apis",
    ),
)


class OpenAIStyleOut(BaseModel):
    class Choice(BaseModel):
        class Message(BaseModel):
            content: str
            role: str = "assistant"
            function_call: Optional[dict] = None
            tool_calls: Optional[dict] = None
            refusal: Optional[dict] = None

        message: Optional[Message] = None
        finish_reason: Optional[str] = "stop"
        index: int = 0
        logprobs: Optional[dict] = None

    id: str
    choices: List[CompletionOut.Choice] = []
    created: int = int(datetime.now().timestamp())
    model: str = "sparticle-agent"


class OpenAIStyleChunkOut(BaseModel):
    class Choice(BaseModel):
        class Delta(BaseModel):
            content: str
            role: str = "assistant"
            refusal: str = None

        delta: Union[Delta, Dict]
        index: int = 0
        finish_reason: str = None

    id: str
    choices: List[Choice] = []
    object: str = "chat.completion.chunk"
    created: int = int(datetime.now().timestamp())
    model: str = "sparticle-agent"
    system_fingerprint: str = "fp_44709d6fcb"


class HisTrafficInformationIn(BaseModel):
    query: str = Field("", description="user query")
    encrypted_phone: str = Field("", description="encrypted phone number")


class HisTrafficInformationOut(BaseModel):
    response: str = Field("", description="response")
    phone_number: str = Field("", description="phone number")
    timestamp: int = Field(0, description="timestamp")


class QuestionAgent(BaseModel):
    question: str = Field(
        max_length=2000,
        default="",
    )
    ai_id: str = Field("", description="Robot ID\nIt is recommended to use uuid")
    session_id: str = Field(
        description="Session ID\nIt is recommended to use uuid",
        max_length=100,
        default="",
    )
    params: Optional[Dict[str, Any]] = Field(
        None,
        description="调试参数，包含以下字段：\n"
        "- messages: List[Dict[str, str]] 消息列表\n"
        "- system: str 系统提示词\n"
        "- tools: List[Dict[str, Any]] 工具列表\n"
        "- function_call: Union[Dict[str, str], str] 函数调用配置",
    )
    extra_context: Optional[Dict[str, Any]] = Field(
        None, description="额外的上下文信息，用于提供动态背景信息"
    )
    base_model = "claude_35_sonnet"
    agent_mode = "chat"

    stream: Optional[bool] = False
    stream_obj: Optional[bool] = False
    with_images: Optional[bool] = None
    debug_with_messages: Optional[bool] = Field(
        default=False,
        description="Debug only, use `debug_with_messages: true, stream: false` to get prompt messages",
    )
    dynamic_background: Optional[Union[str, list]] = Field(
        default="",
        description="json格式字符串或者字典, 动态背景背景信息, 用于添加到agent的meta prompt中",
    )


class PlanCreate(BaseModel):
    """
    Request model for creating a new plan.

    Contains basic plan information, validity period settings, and various quota limits.
    All quota fields set to None indicate unlimited resources for that category.
    """

    # Basic information
    name: str = Field(
        ..., max_length=200, description="Plan name, maximum 50 characters"
    )
    description: Optional[str] = Field(None, description="Plan description, optional")
    price: Optional[Decimal] = Field(
        None,
        ge=0,
        description="Plan price in JPY, optional, must be greater than or equal to 0",
    )

    plan_type: models.PlanType = Field(
        models.PlanType.BASIC, description="Plan type, default is basic plan"
    )

    # Message quota settings
    message_quota: Optional[int] = Field(
        None,
        ge=0,
        description="Message quota amount, None means unlimited, non-None value must be greater than or equal to 0",
    )
    message_quota_grant_type: QuotaGrantType = Field(
        QuotaGrantType.MONTHLY,
        description="Message quota grant type, default is monthly reset",
    )

    # Multi-modal parsing quota
    multi_modal_parsing_quota: Optional[int] = Field(
        None,
        ge=0,
        description="Multi-modal parsing file quota, None means unlimited, non-None value must be greater than or equal to 0",
    )

    # Web page learning quota
    web_page_quota: Optional[int] = Field(
        None,
        ge=0,
        description="Web page learning quota, None means unlimited, non-None value must be greater than or equal to 0",
    )

    # Storage quota
    storage_quota: Optional[int] = Field(
        None,
        ge=0,
        description="Storage capacity quota in bytes, None means unlimited, non-None value must be greater than or equal to 0. Frontend should send byte count, backend stores and calculates in bytes.",
    )

    # Bot count quota
    bot_quota: Optional[int] = Field(
        None,
        ge=0,
        description="Bot count quota, None means unlimited, non-None value must be greater than or equal to 0",
    )

    @validator("plan_type")
    def validate_plan_type(cls, v):
        """Validate plan type, TRIAL plans cannot be created via API"""
        if v == models.PlanType.TRIAL:
            raise ValueError("Cannot create TRIAL plans via API")
        return v

    class Config:
        """Pydantic configuration"""

        schema_extra = {
            "example": {
                "name": "Enterprise Plan",
                "description": "Advanced plan for small to medium businesses",
                "price": 10000,
                "plan_type": "basic",
                "message_quota": 10000,
                "message_quota_grant_type": "monthly",
                "multi_modal_parsing_quota": 1000,
                "web_page_quota": 5000,
                "storage_quota": ***********,  # 10GB (10 * 1024 * 1024 * 1024 bytes)
                "bot_quota": 50,
            }
        }


PlanIn = PlanCreate


AddOnOutBase = pydantic_model_creator(
    AddOn,
    exclude=("deleted_at", "subscriptions", "created_at"),
)


PlanSubscriptionBaseOut = pydantic_model_creator(
    models.PlanSubscription,
    exclude=("deleted_at", "user"),
)


class PlanSubscriptionDetailOut(PlanSubscriptionBaseOut):
    user: UserOutBase
    plan: PlanOutBase


class PlanSubscriptionCreate(BaseModel):
    """Schema for creating a plan subscription"""

    user_id: UUID = Field(..., description="UUID of the user to subscribe")
    plan_id: UUID = Field(..., description="UUID of the plan to subscribe")
    start_at: Optional[datetime] = Field(
        None,
        description="Subscription start time (UTC). If not provided, current time will be used.",
    )
    duration_unit: DurationUnit = Field(
        ..., description="Duration unit for the subscription (day or month)"
    )
    duration_length: int = Field(
        ...,
        gt=0,
        description="Duration length for the subscription, must be greater than 0",
    )


PlanSubscriptionIn = PlanSubscriptionCreate


AddOnSubscriptionBaseOut = pydantic_model_creator(
    models.AddOnSubscription,
    exclude=("deleted_at", "user"),
)


class AddOnSubscriptionResponse(AddOnSubscriptionBaseOut):
    """Schema for add-on subscription responses"""

    user: UserOutBase
    add_on: AddOnOutBase


AddOnSubscriptionOut = AddOnSubscriptionResponse


class AddOnSubscriptionCreate(BaseModel):
    """Schema for creating an add-on subscription"""

    user_id: UUID = Field(
        ..., description="UUID of the user to subscribe to the add-on"
    )
    add_on_id: UUID = Field(..., description="UUID of the add-on to subscribe to")
    start_at: Optional[datetime] = Field(
        None,
        description="Subscription start time (UTC). If not provided, current time will be used.",
    )
    duration_unit: DurationUnit = Field(
        ..., description="Duration unit for the subscription (day or month)"
    )
    duration_length: int = Field(
        ...,
        gt=0,
        description="Duration length for the subscription, must be greater than 0",
    )


AddOnSubscriptionIn = AddOnSubscriptionCreate


class AdminUserOut(UserOutBase):
    active_plan_subscription: Optional["PlanSubscriptionBaseOut"] = None
    active_add_on_subscriptions: List["AddOnSubscriptionBaseOut"] = []

    class PydanticMeta:
        exclude = ("deleted_at",)


class AgentChatIn(BaseModel):
    ai_id: Optional[Union[str, UUID]] = Field(
        "", description="Robot ID\nIt is recommended to use uuid"
    )
    agent_name: Optional[str] = Field(
        "", description="Agent name, default is empty string"
    )
    session_id: Optional[Union[str, UUID]] = Field(
        "", description="Session ID\nIt is recommended to use uuid", max_length=100
    )
    history: Optional[List[Dict[str, str]]] = Field(
        default_factory=list,
        description="Conversation history, list of dictionaries with keys 'role' and 'content'",
    )
    dataset_ids: Optional[List[Union[str, UUID]]] = Field(
        default_factory=list,
        description="List of dataset IDs to be used in the conversation",
    )
    user_input: Optional[str] = Field(
        default="",
        description="User input content, default is empty string",
        max_length=5000,
    )
    stream: Optional[bool] = Field(default=False)


class AgentChatOut(BaseModel):
    status: Optional[str] = "success"
    error_message: Optional[str] = None
    answer: Optional[str | dict | List] = None
    messages: Optional[List] = None
    reference_list: Optional[List] = None


# New model for Addon Subscription Info (Definition + Subscription Dates)
class AddonSubscriptionInfo(BaseModel):
    # Fields from Addon model itself (definition)
    id: UUID  # Addon ID
    name: str
    description: Optional[str] = None
    target_field: str  # The quota type this addon affects
    value: Optional[int] = None  # The value this addon provides for the target_field
    price: Optional[float] = None
    # Fields from AddonSubscription model (subscription specific)
    subscription_id: UUID  # AddonSubscription ID
    start_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None


class PlanSubscriptionWithUsage(PlanSubscriptionBaseOut):
    """PlanSubscription with plan details and usage information"""

    plan: PlanOutBase
    usage: PlanQuotaDetails


class AddOnSubscriptionWithUsage(AddOnSubscriptionBaseOut):
    """AddOnSubscription with add_on details and usage information"""

    add_on: AddOnOutBase
    usage: QuotaDetails


class UserQuotaInfo(BaseModel):
    """Schema for user quota information"""

    user: UserOutBase
    active_plan_subscription: Optional[PlanSubscriptionWithUsage] = Field(default=None)
    active_add_on_subscriptions: List[AddOnSubscriptionWithUsage] = Field(
        default_factory=list
    )
    effective_quotas: UserEffectiveQuotas

    class Config:
        allow_population_by_field_name = True
        schema_extra = {
            "example": {
                "user": {
                    "id": "678dbc67-37e9-4892-a453-06cfe6b292cd",
                    "user_id": "google-oauth2|115768381226062133088",
                    "email": "<EMAIL>",
                    "name": "JiangYing",
                    "picture": "https://lh3.googleusercontent.com/a/AAcHTtfrI87cfkvKQgiVw4STtvJTsrKSa9QZIj-aDAo0Nw=s96-c",
                    "locale": "zh",
                    "corporate": True,
                    "is_independent": False,
                },
                "active_plan_subscription": {
                    "id": "ff600a40-c1ca-4a50-a9ae-d8f9a4c33a86",
                    "created_at": "2025-05-15T00:45:39.150063+00:00",
                    "updated_at": "2025-05-15T00:45:39.150087+00:00",
                    "plan": {
                        "id": "94e7744f-acb1-4e78-b92b-d0b132553e79",
                        "created_at": "2025-05-14T04:40:18.664780+00:00",
                        "updated_at": "2025-05-14T04:40:18.664817+00:00",
                        "plan_type": "basic",
                        "name": "Starter11111--6d05c242-1d0e-40b1-8a83-e7a95fc6d5e0",
                        "price": 49.8,
                        "description": "「スタートアップ/個人事業主向け」最小限のコストでAIチャットボット導入",
                        "message_quota": None,
                        "message_quota_grant_type": "monthly",
                        "web_page_quota": None,
                        "web_page_quota_grant_type": "monthly",
                        "multi_modal_parsing_quota": 10,
                        "multi_modal_parsing_quota_grant_type": "monthly",
                        "storage_quota": None,
                        "bot_quota": None,
                    },
                    "cancelled_at": None,
                    "start_at": "2025-05-15T00:45:38.517995+00:00",
                    "expires_at": "2025-06-15T00:45:38.517995+00:00",
                    "user_id": "678dbc67-37e9-4892-a453-06cfe6b292cd",
                    "usage": {
                        "bot_quota": {"used": 0, "limit": None, "available": None},
                        "message_quota": {"used": 1, "limit": None, "available": None},
                        "web_page_quota": {"used": 0, "limit": None, "available": None},
                        "multi_modal_parsing_quota": {
                            "used": 0,
                            "limit": 10,
                            "available": 10,
                        },
                    },
                },
                "active_add_on_subscriptions": [
                    {
                        "id": "57675c9d-ed2c-4c4b-a412-6f8d1cf63ae0",
                        "created_at": "2025-05-14T17:15:53.113441+00:00",
                        "updated_at": "2025-05-14T17:15:53.113469+00:00",
                        "add_on": {
                            "id": "60e6c622-a913-490a-9315-1e8dc92ebea8",
                            "updated_at": "2025-05-14T17:12:43.701306+00:00",
                            "name": "add-on111-866858b0-d176-453f-b509-1a9e8d66a55a",
                            "description": "add-on111 234",
                            "price": 0.0,
                            "target_field": "web_page_quota",
                            "value": 4,
                        },
                        "cancelled_at": None,
                        "start_at": "2025-05-14T17:15:53.112171+00:00",
                        "expires_at": "2025-06-14T17:15:53.112171+00:00",
                        "user_id": "678dbc67-37e9-4892-a453-06cfe6b292cd",
                        "usage": {"used": 0, "limit": 4, "available": 4},
                    }
                ],
                "effective_quotas": {
                    "bot_quota": {"used": 270, "limit": None, "available": None},
                    "message_quota": {"used": 2, "limit": None, "available": None},
                    "web_page_quota": {"used": 0, "limit": None, "available": None},
                    "multi_modal_parsing_quota": {
                        "used": 0,
                        "limit": 10,
                        "available": 10,
                    },
                    "is_corporate": False,
                },
            }
        }
