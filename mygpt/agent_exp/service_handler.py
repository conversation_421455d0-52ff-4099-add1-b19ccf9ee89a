"""
GBase服务需要使用的一些工具
"""

import asyncio
from typing import (
    Optional,
    Union,
    Any,
    Dict,
    List,
    AsyncIterator,
    AsyncIterable,
    Iterator,
    Iterable,
)
from loguru import logger as logging
from typing import Union, Optional
from uuid import UUID
from skywalking.decorators import trace
from mygpt.agent_exp.core.mod.langchain_core_messages_ai import AIMessage
from mygpt.agent_exp.core.mod.langchain_core_messages_human import HumanMessage
from mygpt.agent_exp.core.storage import agent_reply_postprocess_handler
from mygpt.enums import (
    AIConfigType,
    MessagePageInfo,
    AgentType,
    StreamingMessageDataFormat,
)
from mygpt.error import OperationFailedException
from mygpt.models import Robot, Session, SessionUser, SessionMessage
from mygpt.openai_utils import (
    RedisChatMessageHistory,
    update_message_to_finished,
    task_done_exception_logging_callback,
)
from langchain_community.chat_message_histories.in_memory import ChatMessageHistory

from mygpt.agent_exp.agent_factory import AgentFactory
from mygpt.agent_exp.utils.using_util import covert_mod_messages

from mygpt.schemata import QuestionIn, OpenAIStyleOut, OpenAIStyleChunkOut, StreamOut
from mygpt.session_metrics import (
    create_or_update_session_user_count,
    create_session_message_page_access_metrics,
    create_or_update_session_message_count,
)
from mygpt.utils import num_tokens_for_langchain_messages
from mygpt.settings import RedisClient

from mygpt.openai_utils import (
    chat_acreate,
    get_chat_history,
    get_chat_history_turbo,
    get_final_chat_context,
    user_intent,
    user_intent_detect,
)

from fastapi import (
    APIRouter,
    Body,
    Depends,
    File,
    Form,
    Header,
    HTTPException,
    Request,
    Security,
    UploadFile,
)


@trace()
async def save_message_question(
    ai_obj: Robot,
    session_id: str,
    user_id: str,
    anonymous_username: str,
    question: str,
    message_id: UUID,
    page_info: Optional[dict] = None,
    created_ip: str = "",
    created_by: str = "",
    is_test: bool = False,
    user_identifier: str = "",
):
    """从question.py文件中copy过来的函数, 防止可能的修改带来的不可用问题"""
    # 记录页面访问信息
    if page_info:
        url = page_info.get("url")
        title = page_info.get("title")
        source = (
            page_info.get("source")
            if page_info.get("source")
            else MessagePageInfo.API.value
        )
    else:
        url = ""
        title = ""
        source = MessagePageInfo.API.value
    # create session if not exist
    session_obj = await Session.get_or_none(session_id=session_id, robot_id=ai_obj.id)
    if not session_obj:
        # 判断是否是新用户
        is_new_user_flag = False
        if created_by:
            is_new_user_flag = await Session.filter(
                robot_id=ai_obj.id, created_by=created_by
            ).exists()
        elif created_ip:
            is_new_user_flag = await Session.filter(
                robot_id=ai_obj.id, created_ip=created_ip
            ).exists()
        else:
            is_new_user_flag = True

        users = [created_by if created_by else created_ip]

        new_users = users if is_new_user_flag else []
        old_users = [] if is_new_user_flag else users

        session_obj = await Session.create(
            session_id=session_id,
            robot=ai_obj,
            title="",
            created_by=created_by,
            created_ip=created_ip,
            is_test=is_test,
            source=source,
            user_identifier=user_identifier,
        )
        if not is_test:
            # 统计创建session的用户数
            asyncio.create_task(
                create_or_update_session_user_count(
                    ai_obj.id, new_users=new_users, old_users=old_users
                )
            )
    # add to sessionuser
    await SessionUser.add_if_not_exist(session_id, user_id, anonymous_username)

    # add to sessionmessage
    session_message_exist = await SessionMessage.exists(
        id=message_id,
    )
    if session_message_exist:
        raise OperationFailedException("Message already exists")

    question_obj = await session_obj.add_message(
        user_id,
        anonymous_username,
        question,
        message_id,
        is_test=is_test,
        title=title,
        url=url,
    )

    # 统计session的消息数
    tasks = [
        create_session_message_page_access_metrics(ai_obj.id, url, title, 1, source)
    ]
    if not is_test:
        tasks.append(create_or_update_session_message_count(ai_obj.id, 1))
    asyncio.gather(*tasks)

    return question_obj


@trace()
async def pre_question(
    question: QuestionIn,
    ai_obj: Robot,
    user_id: str,
    created_ip,
    created_by,
):
    """从question.py文件中copy过来的函数, 防止可能的修改带来的不可用问题"""
    # 创建一个任务列表
    tasks = [
        save_message_question(
            ai_obj,
            question.session_id,
            user_id,
            question.anonymous_username,
            question.question,
            question.message_id,
            question.page_info,
            created_ip,
            created_by,
            is_test=question.is_test,
            user_identifier=question.user_identifier or "",
        ),
        get_chat_history(question.session_id, 3000),
    ]

    # 使用 asyncio.gather 运行所有任务
    return await asyncio.gather(*tasks)


def format_open_ai_response(content, message_id):
    """使用OpenAI Style的response格式对消息进行封装
    暂时先简单实现, 如果后续有需要可以继续强化扩展.
    """
    resp = OpenAIStyleOut(id=str(message_id))
    choice = OpenAIStyleOut.Choice()
    choice.message = choice.Message(content=content)
    resp.choices.append(choice)
    return resp


def format_open_ai_chunk_response(
    chunk: str, message_id, finish_reason: str = None, to_json_str: bool = True
):
    """
    使用OpenAI Style的response的chunk格式对消息进行封装
    暂时先简单实现, 如果后续有需要可以继续强化扩展.
    """
    if finish_reason:
        delta = dict()
    else:
        delta = OpenAIStyleChunkOut.Choice.Delta(content=chunk)
    choice = OpenAIStyleChunkOut.Choice(delta=delta)
    choice.finish_reason = finish_reason
    resp = OpenAIStyleChunkOut(id=str(message_id))
    resp.choices.append(choice)
    if to_json_str:
        return resp.json()
    return resp


async def answer_agent(
    robot: Robot,
    question_in: QuestionIn,
    question_record_obj,
    event: asyncio.Event,
    user_id: str,
    request: Request,
    verbose: bool = True,
    agent_type: str = AgentType.REACT,
):
    """使用agent进行问答的主方法, 放在Agent模块中进行管理"""
    history, count = await get_chat_history_turbo(question_in.session_id, 6)
    chat_history = covert_mod_messages(history)
    if question_in.with_images is None:
        question_in.with_images = (
            robot.get_config(AIConfigType.ENABLE_IMAGES).lower() == "true"
        )
    env = {
        "robot": robot,
        "question_in": question_in,
        "question_record_obj": question_record_obj,
        "chat_history": chat_history,
        "event": event,
        "user_id": user_id,
        "request": request,
    }
    agent = AgentFactory.create_agent(
        robot,
        question_in=question_in,
        chat_history=chat_history,
        verbose=verbose,
        agent_type=agent_type,
    )
    # reply, thought_step_count = await agent.run(question_in, chat_history, env=env)
    resposne = await agent.run(question_in, chat_history, env=env)
    if isinstance(resposne, tuple):
        reply, thought_step_count = resposne
    else:
        reply = resposne
        thought_step_count = 0
    logging.info(
        f"【{question_in.message_id}】agent reply: {reply}, thought_step_count: {thought_step_count}"
    )
    if isinstance(reply, Iterator):
        # 如果是迭代器, 说明要进行流式返回, 直接返回迭代器
        return reply

    resp = env.get("resp")
    if reply:
        # reply 不为空, 说明是正常的字符串, 需要组件resp
        # 判断reply类型
        if reply == "<|||bos|||>resp<|||eos|||>":
            # reply以标记形式返回, 最终结果需要获取resp对象中的content.
            # reply以标记形式返回, 数据库等后处理已经在工具内完成.
            return resp
        # 处理数据写库等操作
        asyncio.create_task(
            agent_reply_postprocess_handler(question_in, reply, chat_history)
        )
        return reply
    else:
        # reply 为空, 需要获取env中的resp对象, 直接返回即可
        # 流式情况下, reply为空, 写库操作在工具内部进行处理. (因为需要快速与现有的RAG进行快速整合, 写库的操作在已有的RAG内部完成)
        resp = env.get("resp")
        return resp
