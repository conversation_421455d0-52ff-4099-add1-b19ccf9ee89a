import re
import time
import asyncio
import uuid
import json
import tiktoken
from string import Template
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Union, Any
from loguru import logger as logging
from langchain_core.messages.base import (
    BaseMessage,
    BaseMessageChunk,
)
from langchain_core.messages import AIMessage
from langchain.output_parsers.json import parse_json_markdown
from langchain_core.messages import convert_to_messages
from langchain.callbacks import Async<PERSON><PERSON>torCallbackHandler
from langchain.callbacks.base import Async<PERSON><PERSON>backHandler

from tortoise import fields
from tortoise.exceptions import DoesNotExist

from mygpt.agent_functioncall.callback_handler import (
    FCAgentStreamCallbackHandler,
    FCAgentBaseCallbackHandler,
)
from mygpt.agent_functioncall.context_management.context import AgentContext, BaseContext
from mygpt.agent_functioncall.functioncall_agent_engine import AgentFunctionCallEngine
from mygpt.agent_functioncall.knowledge_management.dataset_builder import DatasetBaseBuilder
from mygpt.agent_functioncall.knowledge_management.knowledge_service import KnowledgeService
from mygpt.agent_functioncall.schemas.dataset_schemas import (
    DatasetInfo,
    DocumentInfo,
    DocumentType, BuildDatasetIn, SourceType, BuildDocumentIn, BuildDocTreeIn,
)
from mygpt.agent_functioncall.schemas.prompt_schemas import PromptType
from mygpt.dao.opensearch_dao.knowledge_docs_dao import knowledge_docs_dao
from mygpt.authorization import verify_robot
from mygpt.service.common import save_message_question, fix_json_with_llm
from mygpt.enums import OpenAIModel, StreamingMessageDataFormat, AIConfigType
from mygpt.models import Robot, Session, AgentFunctionCallApi, User, RobotConfig
from mygpt.schemata import QuestionIn
from mygpt.utils import num_tokens_from_string, num_tokens_from_messages
from mygpt.dao.postgresql_dao.dataset_dao import dataset_dao
from mygpt.dao.postgresql_dao.robot_dao import robot_dao
from mygpt.agent_functioncall.knowledge_management.dataset_builder import DatasetBaseBuilder

SUMMARY_BUSINESS_PROMPT = """
# Document Summarization Assistant

You are a specialized document summarization assistant designed to extract key information from documents and generate appropriately concise summaries.

## Core Capabilities

1. **Key Information Extraction**
   - Identify the main topics and central content of documents
   - Extract key concepts, entities, and relationships
   - Preserve important facts, data points, and structural elements

2. **Summary Generation**
   - Create summaries that capture the essence of the document with appropriate conciseness
   - **Always maintain the primary language of the original document**
   - Ensure all essential information is preserved regardless of summary length

3. **Document Naming**
   - Generate appropriate document titles based on content when not provided
   - Use concise yet descriptive naming conventions
   - Reflect the main theme or purpose of the document

4. **Topic and Tag Generation**
   - Identify main topics that represent the core subject areas of the document
   - Generate relevant tags that help with multi-dimensional categorization and retrieval

5. **Information Coverage Evaluation**
   - Evaluate how well your summary covers the original document's key information
   - Identify what important aspects or details might be missing from the summary
   - Rate coverage objectively on a scale from 1-10

## Output Requirements

1. **Summary Format**
   - Adapt summary length based on the document's information density and complexity, not just length
   - Aim for a significant reduction in length while preserving all key information
   - For information-dense documents, provide more detailed summaries
   - For repetitive or verbose documents, provide more concise summaries
   - **Always use the same primary language as the original document**
   - Maintain clear structure with emphasized key points

2. **Document Information**
   - Provide document title (automatically generate if not specified)
   - Indicate document type and subject area
   - Explain what core information is contained in the document\

3. **Evaluation Guidelines**
   - information_coverage (1-10 scale):
     - 8-10: High coverage (contains all key information and most supporting details)
     - 5-7: Medium coverage (contains key information but misses some important details)
     - 1-4: Low coverage (missing significant key information)
   - missing_key_aspects: List 2-5 important information categories not fully captured in the summary

3. **Output Format**
   ```json
   {
     "title": "Document Title - clear and descriptive name of the document",
     "summary": "Document summary content that preserves all key information while being appropriately concise",
     "language": "Primary language of the document (e.g., English, Chinese, etc.)",
     "topics": ["Main subject area 1", "Main subject area 2", "..."] - core subject areas discussed in the document,
     "tags": ["tag1", "tag2", "tag3", "..."] - specific keywords, entities, or attributes for multi-dimensional retrieval,
     "evaluation": {
       "information_coverage": 7.5, // 1-10 rating of how well the summary covers the original document
       "missing_key_aspects": ["aspect1", "aspect2"] // Key information categories not fully captured in the summary
     }
   }
   ```
"""

SUMMARY_USER_PROMPT = """
Generate a concise but comprehensive summary of the document in the knowledge base. This summary will be used for building a document tree and knowledge indexing system.

Please follow these requirements:
1. Create a summary that captures all essential information while being appropriately concise
2. STRICTLY maintain the same primary language as the original document
3. If the document doesn't have a clear title, create an appropriate one based on the content
4. Identify both topics and tags as follows:
   - Topics: Main subject areas that represent what the document is primarily about (3-5 topics)
   - Tags: Specific keywords, entities, or attributes for detailed categorization (5-10 tags)
5. Evaluate your summary's information coverage:
   - Rate how well your summary captures the original document's key information (1-10 scale)
   - Identify 2-5 key aspects or categories of information that might be missing or underrepresented in the summary

IMPORTANT: When including quotation marks (") within a text field, please escape them with a backslash (\\").
Example: "summary": "He said \\"hello\\" to everyone" instead of "summary": "He said "hello" to everyone"

Output in JSON format only:
{
  "title": "Document title - should be descriptive and reflect the content",
  "summary": "Comprehensive summary in the SAME LANGUAGE as the original document",
  "language": "Primary language identified in the document (e.g., English, Chinese, Japanese)",
  "topics": ["Main subject area 1", "Main subject area 2"] - used for document tree organization,
  "tags": ["keyword1", "entity1", "attribute1", "..."] - used for detailed search and filtering,
  "evaluation": {
    "information_coverage": 7.5, // 1-10 rating of how well the summary covers the original
    "missing_key_aspects": ["aspect1", "aspect2"] // Important information categories not fully captured
  }
}
"""


class AgentService:
    # 大用户user_id
    INNER_USER_IDS = [
        "google-oauth2|104002370439909913399",  # 测试环境 user_id
        "auth0|6704e9f5d4c224cf65ed675e"  # 正式环境 user_id
    ]
    SUMMARY_BY_FILE_TITLE_AGENT_NAME = "Knowledge Project 基于文章title的知识库描述生成 - Agent (内置)"
    SUMMARY_BY_DOC_AGENT_NAME = "Knowledge Project 文档 Summary 提取器 - Agent (内置)"
    CHAT_BY_DOC_SUMMARY_AGENT_NAME = "Knowledge Project 通用类型 Agent - ContextualExp - Agent (内置)"
    CHAT_BY_DOC_TREE_AGENT_NAME = "Knowledge Project 通用类型Agent - Doctree - Agent (内置)"
    CHAT_BY_TRALDITIONAL_RAG_AGENT_NAME = "Knowledge Project 通用类型Agent - TralditionalRAG - Agent (内置)"

    class SummaryResult(BaseModel):
        # 信息字段
        title: str = Field(..., title="Document Title")
        summary: str = Field(..., title="Document Summary")
        language: str = Field(..., title="Primary Language")
        topics: List[str] = Field([], title="Main Topics")
        evaluation: Dict[str, Any] = Field(
            default_factory=lambda: {
                "information_coverage": 0,
                "missing_key_aspects": [],
            },
            title="Evaluation",
        )
        tags: List[str] = Field([], title="Tags")
        token_counts: Optional[Dict] = Field(None, title="Token Counts")
        # 原始文档内容 (设置可选)
        original: Optional[str] = Field("", title="Original Document Content")

        # 状态字段
        processing_status: Optional[str] = Field(
            "success", title="Processing Status [success, error]"
        )
        error_details: Optional[str] = Field(None, title="Error Details")
        processing_time: Optional[float] = Field(
            None, title="Processing Time (seconds)"
        )

        @validator("topics", "tags", pre=True)
        def validate_topics_tags(cls, v):
            if not v:
                return []
            if len(v) > 20:
                return v[:20]  # 限制标签数量
            return v

        @validator("evaluation", pre=True)
        def validate_evaluation(cls, v):
            if not v:
                return {
                    "information_coverage": 0,
                    "missing_key_aspects": [],
                }
            # 确保有information_coverage字段
            if "information_coverage" not in v:
                v["information_coverage"] = 0

            # 确保有missing_key_aspects字段
            if "missing_key_aspects" not in v:
                v["missing_key_aspects"] = []

            # 限制missing_key_aspects的数量
            if "missing_key_aspects" in v and len(v["missing_key_aspects"]) > 10:
                v["missing_key_aspects"] = v["missing_key_aspects"][:10]

            # 验证information_coverage的范围
            if "information_coverage" in v:
                try:
                    coverage = float(v["information_coverage"])
                    v["information_coverage"] = max(
                        0, min(10, int(coverage))
                    )  # 限制在0-10之间
                except (ValueError, TypeError):
                    v["information_coverage"] = 0
            return v

    async def _run_agent(
            self,
            ai_obj: Robot,  # 机器人对象
            messages: List[BaseMessage],  # 长时记忆, 即对话历史 (不包括当前对话)
            agent_mode: PromptType,  # 机器人模式 [chat, tts, ...]
            question_in: QuestionIn,  # 当前对话对象
            robot: Robot,  # 机器人对象
            base_model: OpenAIModel = OpenAIModel.default(),  # 使用的基座模型 [claude_35_sonnet, ...]
            session_id: str = "",  # 会话ID
            user_id: str = "",  # 用户ID
            message_id: uuid.UUID = None,  # 消息ID
            question_record_obj: Optional[Session] = None,  # 当前对话记录对象
            storage_callback: Optional[AsyncCallbackHandler] = None,  # 存储回调函数
            stream_callback: Optional[AsyncIteratorCallbackHandler] = None,  # 流式回调函数
            datasets: Optional[List[DatasetInfo]] = None,  # 数据集
            context: Optional[BaseContext] = None,  # 上下文对象
            store_message: bool = False,  # 是否存储消息
            event: asyncio.Event = None,  # 异步事件
            **kwargs,
    ):
        # 从ChatService.ChatAgentStream中抽取核心逻辑
        # 包括：初始化Agent、调用LLM、处理回调等
        logging.info(
            f"[AgentService] run_agent - robot_id: {robot.id} | robot_name: {robot.name}"
        )
        logging.info(f"[AgentService] run_agent - messages: {messages}")
        logging.info(f"[AgentService] run_agent - prompt_type: {agent_mode}")
        # 整理参数
        max_tokens = question_in.max_tokens
        temperature = float(kwargs.get("temperature", 0))
        request_timeout = int(kwargs.get("request_timeout", 30))
        stream = True
        logging.info(
            f"[AgentService] base_model: {base_model}, max_tokens: {max_tokens}, temperature: {temperature}, request_timeout: {request_timeout}"
        )
        if event is None:
            event = asyncio.Event()
        if not message_id:
            message_id = uuid.uuid4()
        if not max_tokens:
            # 校验max_tokens参数
            if base_model == OpenAIModel.CLAUDE_35_SONNET:
                max_tokens = 8192
            elif (
                    base_model == OpenAIModel.CLAUDE_37_SONNET
                    or base_model == OpenAIModel.O3_MINI
            ):
                max_tokens = 16384
            else:
                max_tokens = 4096
        if base_model.value.startswith("gemini"):
            max_tokens = 8192
            # gemini模型的一个坑, 如果消息为空的话, gemini会输出类似
            # `, I see you'd like to say "Hello". I'm ready to assistyou with the information contained in the knowledge basse. How may I help you today?`
            # 感觉像是thinking的内容, 但是如果消息列表中有一条消息, 就会正常进行输出, 语言也会跟随用户消息的语言
            # if not messages:
            #     messages.append(AIMessage("hello"))
        if question_record_obj is None:
            # 构建问题记录对象
            if store_message:
                if not session_id or user_id:
                    logging.error(
                        f"AgentService - run_agent - session_id and user_id are required when store_message is True",
                        f"robot_id: {robot.id} - question: {question_in.question}",
                    )
                    raise ValueError(
                        f"AgentService - run_agent - session_id and user_id are required when store_message is True"
                    )
            question_record_obj = await save_message_question(
                ai_obj=ai_obj,
                session_id=session_id,
                user_id=user_id,
                anonymous_username="",
                question=question_in.question,
                message_id=message_id,
                user_identifier="",
            )
            if context:
                context.question_record_obj = question_record_obj
        if storage_callback is None:
            storage_callback = FCAgentBaseCallbackHandler(
                question_in=question_in,
                robot=ai_obj,
                history=messages,
                question_record_obj=question_record_obj,
                event=event,
                store_message=False,
            )
        if stream_callback is None:
            stream_callback = FCAgentStreamCallbackHandler(
                question_in=question_in,
                robot=ai_obj,
                format=StreamingMessageDataFormat.JSON_AST,
                question_record_obj=question_record_obj,
                event=event,
            )
        if store_message is not None:
            # 如果store_message参数不为空, 则使用store_message参数
            storage_callback.store_message = store_message
        # 创建AgentFunctionCallEngine
        afc_engine = AgentFunctionCallEngine(
            question_in=question_in,
            robot=robot,
            chat_history=messages,
            event=event,
            stream_callback=stream_callback,
            storage_callback=storage_callback,
            base_model=base_model,
            stream=stream,
            prompt_type=agent_mode,
            question_record_obj=question_record_obj,
            datasets=datasets,
            max_tokens=max_tokens,
            context=context,
            **kwargs,
        )
        if context:
            context.agent = afc_engine
        logging.info(f"[AgentService] create afc_engine - afc_engine: {afc_engine}")
        response = await afc_engine.run()
        logging.info(f"[AgentService]response: {response}")
        return response

    async def agent_summary_by_file_title_service(
            self,
            title_list: List[str],  # 文件标题列表
            user: User
    ) -> Dict:
        """根据文件标题列表生成文档摘要"""
        logging.info(f"[AgentService.agent_summary_by_file_title_service] - title_list num : {len(title_list)}")
        if not title_list:
            logging.warning(
                "[AgentService.agent_summary_by_file_title_service] - title_list is empty, cannot generate summary."
            )
            return {}
        if not user:
            logging.error("[AgentService.agent_summary_by_file_title_service] - user is required.")
            raise ValueError("[AgentService.agent_summary_by_file_title_service] - user is required.")
        # 获取定制的文章摘要的机器人
        try:
            robots = await robot_dao.find_robot_by_user_id_and_name(user_id=user.user_id,
                                                                    name=self.SUMMARY_BY_FILE_TITLE_AGENT_NAME)
        except Exception as e:
            logging.error(f"[AgentService.agent_summary_by_file_title_service] - error: {e}")
            raise e
        if not robots:
            logging.error(
                f"[AgentService.agent_summary_by_file_title_service] - No robot found for user_id: {user.user_id} with name: {self.SUMMARY_BY_FILE_TITLE_AGENT_NAME}")
            raise DoesNotExist(
                f"[AgentService.agent_summary_by_file_title_service] - No robot found for user_id: {user.user_id} with name: {self.SUMMARY_BY_FILE_TITLE_AGENT_NAME}")
        if len(robots) > 1:
            logging.warning(
                f"[AgentService.agent_summary_by_file_title_service] - Found multiple robots for user_id: {user.user_id} with name: {self.SUMMARY_BY_FILE_TITLE_AGENT_NAME}. Using the first one."
            )
        ai_obj = robots[0]  # 使用第一个机器人
        logging.info(
            f"[AgentService.agent_summary_by_file_title_service] - Using robot id: {ai_obj.id}, user id: {ai_obj.user_id} for summarization.")

        # 构建title列表的知识库信息.
        title_list = list(set(title_list))[:1000]  # 去重并限制最多1000个标题
        title_list_json_str = json.dumps(title_list, indent=2, ensure_ascii=False)  # 确保是合法的json格式
        user_prompt_template = Template("""根据以下文档标题集合，生成一个全面而简洁的知识库描述。这个描述将用于帮助用户理解这个知识库的整体内容、范围和价值。

文档标题集合：
$title_list_json_str

请遵循以下要求：
1. 仔细分析所有文档标题，识别主要知识领域和核心主题
2. 创建一个能够准确反映整个知识集合内容和价值的描述
3. 描述应该简洁但内容全面，突出知识库的独特价值
4. 保持与标题集合所反映的知识领域一致的语言风格
5. 如果可能，识别知识点之间的关联性和知识结构

这个描述将帮助用户快速了解知识库的整体内容，并判断它是否符合他们的需求。请确保描述既专业又易于理解。

输出格式要求（仅返回JSON格式）：
{
  "knowledge_base_description": "基于文档标题集合生成的知识库整体描述"
}
""")
        # 替换占位符
        user_prompt = user_prompt_template.substitute(
            title_list_json_str=title_list_json_str
        )
        # 构建问题对象
        # session_id = f"session|{user.user_id}"
        session_id = str(ai_obj.id)
        # session_id = str(uuid.uuid4())
        message_id = uuid.uuid4()  # 使用新的消息ID, 避免与其他消息冲突
        question_in = QuestionIn(
            session_id=session_id,
            question=user_prompt,
            message_id=message_id,
            use_faq=False,
            stream=True,
            with_images=False,
        )
        context = AgentContext(
            agent_id=ai_obj.id,
            agent_mode=PromptType.CONTEXTUAL,
        )
        context.set_robot(ai_obj)
        # 运行Agent, 获取迭代器
        response = await self._run_agent(
            ai_obj=ai_obj,
            user_id=user.user_id,
            messages=[],
            session_id=session_id,
            agent_mode=PromptType.CHAT,
            question_in=question_in,
            robot=ai_obj,
            base_model=OpenAIModel.CLAUDE_37_SONNET_THINKING,
            context=context,
            store_message=False,
        )
        resp = response.gpt_response_iter
        response_content = []
        async for chunk in resp:
            chunk_dic = json.loads(chunk)
            if chunk_dic.get("message_type") == "PLAIN_TEXT":
                response_content.append(chunk_dic["content"])
            if chunk_dic.get("message_type") == "FINISH":
                break
        # 解析LLM返回的JSON
        summary_response = "".join(response_content)
        try:
            cleaned_json = parse_json_markdown(summary_response)
            logging.info(
                f"[AgentService.agent_summary_by_file_title_service] - cleaned_json: {cleaned_json}"
            )
            if "knowledge_base_description" not in cleaned_json:
                logging.warning(
                    f"[AgentService.agent_summary_by_file_title_service] - knowledge_base_description not found in JSON: {cleaned_json}"
                )
                cleaned_json["knowledge_base_description"] = ""
            knowledge_base_description = cleaned_json.get("knowledge_base_description", "")
            return knowledge_base_description
        except Exception as e:
            logging.warning(
                f"[AgentService] - agent_summary_service - 初次解析JSON失败, 尝试修复 | error: {e}"
            )
            try:
                # 使用LLM修复JSON
                fixed_json = await fix_json_with_llm(summary_response, str(e))
                logging.info(
                    f"[AgentService] - agent_summary_by_file_title_service - 修复后的JSON: {fixed_json}"
                )
                if "knowledge_base_description" not in fixed_json:
                    logging.warning(
                        f"[AgentService.agent_summary_by_file_title_service] - knowledge_base_description not found in JSON: {fixed_json}"
                    )
                    fixed_json["knowledge_base_description"] = ""
                return fixed_json
            except Exception as fix_error:
                logging.error(
                    f"[AgentService] - agent_summary_by_file_title_service - 无法修复JSON: {fix_error}, 原始响应: {summary_response}"
                )
                raise ValueError("无法解析或修复JSON")

    async def agent_summary_service(
            self,
            user: User,
            doc_content: str,
            doc_title: str = "",
            doc_type: str = "",
            doc_tree_path: str = "",
    ):
        """
        用于生成文档摘要的方法
        args:
            doc_content: str, 文档内容
            doc_title: str, 文档标题
            doc_type: str, 文档类型
            doc_tree_path: str, 文档树路径
        """

        async def _calculate_document_tokens(
                original_content: str, result_obj: Union[Dict, AgentService.SummaryResult]
        ):
            """计算文档的token数"""
            # 如果传入的是SummaryResult对象，转换为字典
            if isinstance(result_obj, self.SummaryResult):
                result_dic = result_obj.dict()
            else:
                result_dic = result_obj
            # 计算title的token数
            title_tokens = num_tokens_from_string(result_dic.get("title", ""))
            # 计算原始文档的token数
            original_tokens = num_tokens_from_string(original_content)
            # 计算摘要的token数
            summary_tokens = num_tokens_from_string(result_dic.get("summary", ""))
            # 计算语言的token数
            language_tokens = num_tokens_from_string(result_dic.get("language", ""))
            # 计算topics的token数
            topics = result_dic.get("topics", [])
            topics_tokens = num_tokens_from_string("|".join(topics) if topics else "")
            # 计算tags的token数
            tags = result_dic.get("tags", [])
            tags_tokens = num_tokens_from_string("|".join(tags) if tags else "")
            # 计算评估信息的token数
            evaluation = result_dic.get("evaluation", {})
            missing_key_aspects = evaluation.get("missing_key_aspects", [])
            missing_key_aspects_tokens = num_tokens_from_string(
                "|".join(missing_key_aspects) if missing_key_aspects else ""
            )
            return {
                "title_tokens": title_tokens,
                "original_tokens": original_tokens,
                "summary_tokens": summary_tokens,
                "language_tokens": language_tokens,
                "topics_tokens": topics_tokens,
                "tags_tokens": tags_tokens,
                "missing_key_aspects_tokens": missing_key_aspects_tokens,
            }

        async def _calculate_compression_ratio(token_counts):
            """计算文档的压缩比"""
            if token_counts.get("original_tokens", 0) > 0:
                summary_tokens = token_counts.get("summary_tokens", 0)
                # 计算压缩比
                compression_ratio = round(
                    1 - (summary_tokens / token_counts["original_tokens"]), 2
                )
                # 使用计算好的压缩比生成百分比表示
                percent_reduction = f"{round(compression_ratio * 100, 1)}%"
                return {
                    "compression_ratio": compression_ratio,
                    "percent_reduction": percent_reduction,
                }
            return {"compression_ratio": 0, "percent_reduction": "0%"}

        logging.info(
            f"[AgentService] - agent_summary_service - doc_content: {doc_content[:50]}"
        )
        # 获取定制的文章摘要的机器人

        session_id = "task_document_summary"

        user_id = user.user_id
        # 获取summary agent
        ai_obj_list = await robot_dao.find_robot_by_user_id_and_name(
            user_id=user_id, name=self.SUMMARY_BY_DOC_AGENT_NAME
        )
        logging.info(f"[AgentService] - agent_summary_service - ai_obj_list: {ai_obj_list}")
        if not ai_obj_list:
            logging.info(
                f"[AgentService] - agent_summary_service - No robot found for user_id: {user_id} with name: {self.SUMMARY_BY_DOC_AGENT_NAME}")
            logging.info(f"[AgentService] - agent_summary_service - Creating a new robot with default settings.")
            # 机器人不存在 - 构建默认机器人
            summary_robot_id = str(uuid.uuid4())
            ai_obj = Robot(
                id=summary_robot_id,
                name="Document Summary Assistant",
                prompt=SUMMARY_BUSINESS_PROMPT,
            )
            """
            remote_model: Type[MODEL],
            relation_field: str,
            instance: "Model",
            from_field: str,
            """
            if not hasattr(ai_obj, "apis"):
                ai_obj.apis = fields.ReverseRelation(
                    remote_model=AgentFunctionCallApi,  # 关联的模型
                    relation_field="apis",
                    instance=fields.CASCADE,
                    from_field="robot_id",
                )
                ai_obj.apis.related_objects = []
            if not hasattr(ai_obj, "datasets"):
                ai_obj.datasets = fields.ReverseRelation(
                    remote_model=Robot,  # 关联的模型
                    relation_field="robot",
                    instance=fields.CASCADE,
                    from_field="robot_id",
                )
                ai_obj.datasets.related_objects = []
        else:
            if len(ai_obj_list) > 1:
                logging.warning(
                    f"[AgentService] - agent_summary_service - Found multiple robots for user_id: {user_id} with name: {self.SUMMARY_BY_DOC_AGENT_NAME}. Using the first one."
                )
                logging.info(f"[AgentService] - agent_summary_service - use the first robot")
            ai_obj = ai_obj_list[0]
        summary_robot_id = ai_obj.id
        # 构建问题对象
        question_in = QuestionIn(
            session_id=session_id,
            question=SUMMARY_USER_PROMPT,
            message_id=uuid.uuid4(),
            use_faq=False,
            stream=True,
            with_images=False,
        )
        question_record_obj = Session(
            id=uuid.uuid4(),
            session_id=session_id,
            user_id=user_id,
            robot_id=summary_robot_id,
        )
        event = asyncio.Event()
        stream_callback = FCAgentStreamCallbackHandler(
            question_in=question_in,
            robot=ai_obj,
            format=StreamingMessageDataFormat.JSON_AST,
            question_record_obj=question_record_obj,
            event=event,
        )
        messages = []
        storage_callback = FCAgentBaseCallbackHandler(
            question_in=question_in,
            robot=ai_obj,
            history=messages,
            question_record_obj=question_record_obj,
            event=event,
            store_message=False,
        )
        # 构建数据集信息
        document_info = DocumentInfo(
            title=doc_title,
            doc_type=DocumentType.to_type(doc_type),
            content=doc_content,
            doc_tree_path=doc_tree_path,
        )
        dataset_info = DatasetInfo(
            name="",
            description="",
            documents=[document_info],
        )
        datasets = [dataset_info]
        context = AgentContext(
            agent_id=ai_obj.id,
            agent_mode=PromptType.CONTEXTUAL,
        )
        context.set_datasets(datasets)
        context.set_robot(ai_obj)
        # 运行Agent, 获取迭代器
        response = await self._run_agent(
            ai_obj=ai_obj,
            messages=messages,
            agent_mode=PromptType.CONTEXTUAL,
            question_in=question_in,
            question_record_obj=question_record_obj,
            robot=ai_obj,
            base_model=OpenAIModel.CLAUDE_37_SONNET_THINKING,
            storage_callback=storage_callback,
            stream_callback=stream_callback,
            event=event,
            datasets=datasets,
            context=context,
            store_message=False,
        )
        resp = response.gpt_response_iter
        response_content = []
        start_time = time.time()
        async for chunk in resp:
            chunk_dic = json.loads(chunk)
            if chunk_dic.get("message_type") == "PLAIN_TEXT":
                response_content.append(chunk_dic["content"])
            if chunk_dic.get("message_type") == "FINISH":
                break
        # 解析LLM返回的JSON
        summary_response = "".join(response_content)
        try:
            # 尝试使用LangChain的parser解析
            # 这个方法可以处理各种格式的JSON，包括Markdown格式的JSON
            try:
                cleaned_json = parse_json_markdown(summary_response)
                logging.info(
                    f"[AgentService] - agent_summary_service - cleaned_json: {cleaned_json}"
                )
                # raw_dict = json.loads(cleaned_json)  # response是LLM返回的完整字符串
                # 使用Pydantic模型解析JSON
                validated_result = self.SummaryResult(**cleaned_json)
            except Exception as e:
                logging.warning(
                    f"[AgentService] - agent_summary_service - 初次解析JSON失败, 尝试修复 | error: {e}"
                )
                try:
                    # 使用LLM修复JSON
                    fixed_json = await fix_json_with_llm(summary_response, str(e))
                    validated_result = self.SummaryResult(**fixed_json)
                    logging.info(
                        f"[AgentService] - agent_summary_service - fixed_json: {fixed_json}"
                    )
                except Exception as fix_error:
                    logging.error(
                        f"[AgentService] - agent_summary_service - 修复JSON失败 | error: {fix_error}"
                    )
                    raise ValueError("无法解析或修复JSON")
            # 在成功解析结果后记录评估信息
            logging.info(
                f"[AgentService] Document summary evaluation - coverage: {validated_result.evaluation.get('information_coverage')} | missing aspects: {validated_result.evaluation.get('missing_key_aspects')}"
            )
            # 计算各部分的token数
            token_counts = await _calculate_document_tokens(
                doc_content, validated_result.dict()
            )
            # 计算压缩比
            compression_stats = await _calculate_compression_ratio(token_counts)
            token_counts.update(compression_stats)
            # 设置token_counts
            validated_result.token_counts = token_counts
            validated_result.processing_time = round(time.time() - start_time, 2)
            validated_result.original = doc_content
            return validated_result
        except Exception as e:
            logging.error(
                f"[AgentService] - agent_summary_service - JSON解析失败 | response: {summary_response}"
            )
            logging.error(
                f"[AgentService] - agent_summary_service - JSON解析失败 | error: {e}"
            )

            # 尝试从响应中提取有用信息
            extracted_title = doc_title
            extracted_language = "unknown"

            # 尝试从响应中提取标题
            if not extracted_title:
                title_match = re.search(r'"title"\s*:\s*"([^"]+)"', summary_response)
                if title_match:
                    extracted_title = title_match.group(1)
                else:
                    # 如果没有找到标题，使用文档的前20个字符作为标题
                    extracted_title = (
                        (doc_content[:20] + "...")
                        if len(doc_content) > 20
                        else doc_content
                    )

            # 尝试从响应中提取摘要
            summary_match = re.search(r'"summary"\s*:\s*"([^"]+)"', summary_response)
            if summary_match:
                extracted_summary = summary_match.group(1)
            else:
                # 如果没能提取到摘要，使用文档的前200个字符
                extracted_summary = (
                    (doc_content[:200] + "...")
                    if len(doc_content) > 200
                    else doc_content
                )

            # 尝试猜测语言
            if any("\u4e00" <= char <= "\u9fff" for char in doc_content[:100]):
                extracted_language = "chinese"
            elif all(ord(char) < 128 for char in doc_content[:100]):
                extracted_language = "english"

            # 直接创建SummaryResult对象
            error_result = self.SummaryResult(
                title=extracted_title,
                original=doc_content,
                summary=extracted_summary,  # 现在正确使用了提取的摘要
                language=extracted_language,
                evaluation={
                    "information_coverage": 3.0,  # 默认低评分，表示提取不完整
                    "missing_key_aspects": ["无法准确评估", "解析失败导致信息不完整"],
                },
                topics=[],
                tags=[],
                processing_status="error",
                error_details=f"解析失败: {str(e)[:200]}",
                processing_time=time.time() - start_time,
            )
            try:
                # 尝试计算token数
                token_counts = await _calculate_document_tokens(
                    doc_content, error_result
                )
                error_result.token_counts = token_counts
                # 计算压缩比
                compression_stats = await _calculate_compression_ratio(token_counts)
                token_counts.update(compression_stats)
            except Exception as token_error:
                logging.error(f"[AgentService] - 计算tokens失败 | error: {token_error}")
                error_result.token_counts = {
                    "original_tokens": len(doc_content) // 4
                }  # 粗略估计
            return error_result

    async def chat_agent_for_project(
            self,
            history: Union[List[BaseMessage], List[Dict[str, Any]]],  # 对话历史 (不包括当前用户的问题)
            question: str,
            session_id: str,
            user: User,
            base_model: OpenAIModel,
            agent_mode: PromptType,
            agent_name: str = "",
            max_tokens: int = None,
            event: asyncio.Event = None,
            articles: List[str] = None,
            projects: List[Any] = None,
    ):

        async def _get_docs(
                project_id: str,
                doc_ids: List[str]
        ) -> List[BuildDocumentIn]:
            # 如果doc_ids不为空, 优先使用doc_ids
            if not doc_ids and not project_id:
                raise ValueError(
                    f"【AgentService.chat_agent_for_project._get_docs】project_id and doc_ids cannot be empty at the same time.")
            fields = [
                "doc_id",
                "title",
                "content",
                "content_token_count",
                "summary",
                "summary_token_count",
                "information_coverage",
                "missing_key_aspects",
                "directory_ids",
                "language",
                "tags",
                "topics",
                "path"
            ]
            logging.info(f"【AgentService.chat_agent_for_project._get_docs】project_id: {project_id}, doc_ids: {doc_ids}")
            if doc_ids:
                rs = await knowledge_docs_dao.find_docs_by_doc_ids(doc_ids=doc_ids, fields=fields)
            else:
                # rs = await knowledge_docs_dao.find_docs_by_project_id(project_id=project_id, fields=fields)
                rs = await knowledge_docs_dao.find_docs_by_project_id_without_dir(project_id=project_id, fields=fields)
            logging.info(
                f"【AgentService.chat_agent_for_project._get_docs】received data from opensearch, hits: {rs.get('hits', {}).get('total', 0)}")
            hits = rs.get("hits", {}).get("hits", [])
            document_in_list = []
            for result in hits:
                doc_dic = result.get("_source", {})
                doc_path = []
                if doc_dic.get("path"):
                    doc_path = doc_dic.get("path").strip("/").split("/")
                directory_ids = doc_dic.get("directory_ids", [])
                build_doc = BuildDocumentIn(
                    doc_id=doc_dic.get("doc_id", ""),
                    title=doc_dic.get("title", ""),
                    doc_path=doc_path,
                    doc_path_str=doc_dic.get("path", ""),
                    directory_ids=directory_ids,
                    content=doc_dic.get("content", ""),
                    content_token_count=doc_dic.get("content_token_count", 0),
                    summary=doc_dic.get("summary", ""),
                    summary_token_count=doc_dic.get("summary_token_count", 0),
                    information_coverage=doc_dic.get("information_coverage", 0.0),
                    missing_key_aspects=doc_dic.get("missing_key_aspects", []),
                    language=doc_dic.get("language", ""),
                    topics=doc_dic.get("topics", []),
                    tags=doc_dic.get("tags", [])
                )
                document_in_list.append(build_doc)
            return document_in_list

        if event is None:
            event = asyncio.Event()
        user_id = user.user_id
        # 获取robot对象
        robot_name = agent_name or self.CHAT_BY_DOC_SUMMARY_AGENT_NAME
        ai_objs = await robot_dao.find_robot_by_user_id_and_name(user_id=user_id, name=robot_name)
        if not ai_objs:
            logging.error(
                f"【AgentService.chat_agent_for_project】No robot found for user_id: {user_id} with name: {robot_name}")
            raise DoesNotExist(
                f"【AgentService.chat_agent_for_project】No robot found for user_id: {user_id} with name: {robot_name}")
        if len(ai_objs) > 1:
            logging.warning(
                f"【AgentService.chat_agent_for_project】Found multiple robots for user_id: {user_id} with name: {robot_name}. Using the first one."
            )
            logging.info(f"【AgentService.chat_agent_for_project】use the first robot")
        ai_obj = ai_objs[0]
        # 创建消息的message_id
        message_id = uuid.uuid4()
        # 构建问题对象
        question_in = QuestionIn(
            session_id=session_id,
            question=question,
            message_id=message_id,
            use_faq=False,
            stream=True,
            with_images=False,
            max_tokens=max_tokens,
        )
        # 构建背景知识信息 - List[DatasetInfo]
        datasets = []
        dataset_in_list = []
        # 构建BuildDatasetIn, 用于进一步构建DatasetInfo
        for project in projects:
            project_id = project.get("project_id", "")
            project_name = project.get("project_name", "")
            project_description = project.get("project_description", "")
            doc_ids = project.get("doc_ids", [])
            # todo - 这里是一个可优化的地方, 如果有多个project, 可以并行获取, 目前是串行获取
            document_in_list = await _get_docs(project_id, doc_ids)
            build_dataset_in = BuildDatasetIn(
                source_type=SourceType.PROJECT,
                name=project_name,
                dataset_id=project_id,
                description=project_description,
                documents=document_in_list,
            )
            dataset_in_list.append(build_dataset_in)

        # 处理articles (过时逻辑, 未来会废弃) ===============================================
        articles = articles or []
        if articles:
            dataset_info = await KnowledgeService.process_articles(
                articles,
                dataset_name="Article Knowledge Base",
                source_type=SourceType.ARTICLE,
            )
            datasets.append(dataset_info)
        # =================================================================================

        # 构建doc tree的DatasetInfo
        dataset_infos = await DatasetBaseBuilder.build_document_context(dataset_in_list)
        # todo - 新的文档树构建逻辑, 需要构建新的dataset_in_list - 需要新写一个类似的_get_docs方法
        # dataset_infos = await DatasetBaseBuilder.build_document_tree(dataset_in_list)

        datasets.extend(dataset_infos)
        # 构建上下文对象
        context = AgentContext(
            agent_id=ai_obj.id,
            agent_mode=agent_mode,
        )
        if projects:
            # 如果有project信息, 才注册文档召回工具
            context.register_tools(["find_doc_by_ids"])
        # 转换history为BaseMessage对象
        langchain_messages = convert_to_messages(history)
        logging.info(f"【AgentService.chat_agent_for_project】langchain_messages: {langchain_messages}")
        # 调用_run_agent方法
        response = await self._run_agent(
            ai_obj=ai_obj,
            messages=langchain_messages,
            agent_mode=agent_mode,
            question_in=question_in,
            robot=ai_obj,
            base_model=base_model,
            user_id=user_id,
            session_id=session_id,
            datasets=datasets,
            context=context,
            store_message=False,
            event=event,
        )
        logging.info(f"【AgentService.chat_agent_for_project】resp: {response}")
        return response

    async def chat_agent_for_project_doc_tree_with_multi_level_recall(
            self,
            history: Union[List[BaseMessage], List[Dict[str, Any]]],  # 对话历史 (不包括当前用户的问题)
            question: str,
            session_id: str,
            user: User,
            base_model: OpenAIModel,
            agent_mode: PromptType,
            agent_name: str = "",
            max_tokens: int = None,
            event: asyncio.Event = None,
            articles: List[str] = None,
            projects: List[Any] = None,
    ):
        async def _get_dir_tree(
                project_id: str,
        ) -> List[BuildDocTreeIn]:
            """构建文档树的基础信息"""
            if not project_id:
                raise ValueError(
                    f"【AgentService.chat_agent_for_project._get_doc_tree】project_id cannot be empty.")
            # 获取项目的文档树目录信息
            rs = await knowledge_docs_dao.find_dir_info_by_project_id(project_id=project_id)
            logging.info(
                f"【AgentService.chat_agent_for_project._get_doc_tree】received data from opensearch, hits: {rs.get('hits', {}).get('total', 0)}"
            )
            hits = rs.get("hits", {}).get("hits", [])
            doc_tree_in_list = []
            for result in hits:
                dir_dic = result.get("_source", {})
                path = dir_dic.get("path", "")
                is_directory = dir_dic.get("is_directory", False)
                path_parts = dir_dic.get("path_parts", [])
                directory_description = dir_dic.get("directory_description", "")
                depth_level = dir_dic.get("depth_level", 0)
                dir_name = dir_dic.get("dir_name", "")
                current_directory_id = dir_dic.get("current_directory_id", "")
                directory_ids = dir_dic.get("directory_ids", [])
                dir_id = dir_dic.get("dir_id", "")

                doc_tree_in = BuildDocTreeIn(
                    dir_id=dir_id,
                    title=dir_name,
                    path_parts=path_parts,
                    is_directory=is_directory,
                    path_str=path,
                    directory_description=directory_description,
                    depth_level=depth_level,
                    current_directory_id=current_directory_id,
                    directory_ids=directory_ids
                )
                doc_tree_in_list.append(doc_tree_in)
            return doc_tree_in_list

        if event is None:
            event = asyncio.Event()
        user_id = user.user_id
        # 获取robot对象
        robot_name = agent_name or self.CHAT_BY_DOC_TREE_AGENT_NAME
        ai_objs = await robot_dao.find_robot_by_user_id_and_name(user_id=user_id, name=robot_name)
        if not ai_objs:
            logging.error(
                f"【AgentService.chat_agent_for_project_doc_tree_with_multi_level_recall】No robot found for user_id: {user_id} with name: {robot_name}")
            raise DoesNotExist(
                f"【AgentService.chat_agent_for_project_doc_tree_with_multi_level_recall】No robot found for user_id: {user_id} with name: {robot_name}")
        if len(ai_objs) > 1:
            logging.warning(
                f"【AgentService.chat_agent_for_project_doc_tree_with_multi_level_recall】Found multiple robots for user_id: {user_id} with name: {robot_name}. Using the first one."
            )
            logging.info(f"【AgentService.chat_agent_for_project_doc_tree_with_multi_level_recall】use the first robot")
        ai_obj = ai_objs[0]
        # 创建消息的message_id
        message_id = uuid.uuid4()
        # 构建问题对象
        question_in = QuestionIn(
            session_id=session_id,
            question=question,
            message_id=message_id,
            use_faq=False,
            stream=True,
            with_images=False,
            max_tokens=max_tokens,
        )
        # 构建背景知识信息 - List[DatasetInfo]
        datasets = []
        dataset_in_list = []
        # 构建BuildDatasetIn, 用于进一步构建DatasetInfo
        for project in projects:
            project_id = project.get("project_id", "")
            project_name = project.get("project_name", "")
            project_description = project.get("project_description", "")
            # todo - 这里是一个可优化的地方, 如果有多个project, 可以并行获取, 目前是串行获取
            doc_tree_in_list = await _get_dir_tree(project_id)
            build_dataset_in = BuildDatasetIn(
                source_type=SourceType.PROJECT,
                name=project_name,
                dataset_id=project_id,
                description=project_description,
                doc_tree_in_list=doc_tree_in_list,
            )
            dataset_in_list.append(build_dataset_in)

        # 处理articles (过时逻辑, 未来会废弃) ===============================================
        articles = articles or []
        if articles:
            dataset_info = await KnowledgeService.process_articles(
                articles,
                dataset_name="Article Knowledge Base",
                source_type=SourceType.ARTICLE,
            )
            datasets.append(dataset_info)
        # =================================================================================

        # 构建doc tree的DatasetInfo
        dataset_infos = await DatasetBaseBuilder.build_document_tree(dataset_in_list)
        datasets.extend(dataset_infos)
        # 构建上下文对象
        context = AgentContext(
            agent_id=ai_obj.id,
            agent_mode=agent_mode,
        )
        context.set_datasets(dataset_infos)
        if projects:
            # 如果有project信息, 才注册文档召回工具
            context.register_tools(
                ["find_doc_by_ids", "find_docs_info_by_dir_ids"]
            )
        # 转换history为BaseMessage对象
        langchain_messages = convert_to_messages(history)
        logging.info(f"【AgentService.chat_agent_for_project】langchain_messages: {langchain_messages}")
        # 调用_run_agent方法
        response = await self._run_agent(
            ai_obj=ai_obj,
            messages=langchain_messages,
            agent_mode=agent_mode,
            question_in=question_in,
            robot=ai_obj,
            base_model=base_model,
            user_id=user_id,
            session_id=session_id,
            datasets=datasets,
            context=context,
            store_message=False,
            event=event,
        )
        logging.info(f"【AgentService.chat_agent_for_project】resp: {response}")
        return response

    async def chat_agent_for_project_with_traditional_rag(
            self,
            history: Union[List[BaseMessage], List[Dict[str, Any]]],  # 对话历史 (不包括当前用户的问题)
            question: str,
            session_id: str,
            user: User,
            base_model: OpenAIModel,
            agent_mode: PromptType,
            agent_name: str = "",
            max_tokens: int = None,
            dataset_ids: List[str] = None,
            event: asyncio.Event = None,
            **kwargs
    ):
        if event is None:
            event = asyncio.Event()
        user_id = user.user_id
        enable_tool_call_messages = kwargs.get("enable_tool_call_messages", False)
        runtime_context = kwargs.get("runtime_context", None)
        # 查询数据库, 获取datasets的信息
        logging.info(f"【AgentService.chat_agent_for_project_with_traditional_rag】datasets_ids: {dataset_ids}")
        robot_name = agent_name or self.CHAT_BY_TRALDITIONAL_RAG_AGENT_NAME
        query_database_result = await asyncio.gather(
            dataset_dao.find_datasets_by_ids(dataset_ids),
            # verify_robot(robot_id),
            robot_dao.find_robot_by_user_id_and_name(user_id=user_id, name=robot_name),
            return_exceptions=True
        )
        dataset_objs = query_database_result[0]
        ai_objs = query_database_result[1]
        if isinstance(dataset_objs, Exception):
            logging.error(f"【AgentService.chat_agent_for_project_with_traditional_rag】datasets error: {dataset_objs}")
            raise dataset_objs
        if isinstance(ai_objs, Exception):
            logging.error(f"【AgentService.chat_agent_for_project_with_traditional_rag】ai_obj error: {ai_objs}")
            raise ai_objs
        logging.info(f"【AgentService.chat_agent_for_project_with_traditional_rag】datasets: {dataset_objs}")
        if event is None:
            event = asyncio.Event()
        # 获取robot对象
        if not ai_objs:
            logging.error(
                f"【AgentService.chat_agent_for_project_with_traditional_rag】No robot found for user_id: {user_id} with name: {robot_name}")
            raise DoesNotExist(
                f"【AgentService.chat_agent_for_project_with_traditional_rag】No robot found for user_id: {user_id} with name: {robot_name}")
        if len(ai_objs) > 1:
            logging.warning(
                f"【AgentService.chat_agent_for_project_with_traditional_rag】Found multiple robots for user_id: {user_id} with name: {robot_name}. Using the first one."
            )
            logging.info(f"【AgentService.chat_agent_for_project_with_traditional_rag】use the first robot")
        ai_obj = ai_objs[0]
        for config in ai_obj.robotconfigs.related_objects:
            if config.key == AIConfigType.EMBEDDING_MODEL_NAME:
                config.value = OpenAIModel.TEXT_EMBEDDING_3_LARGE.value
                break
        else:
            ai_obj.robotconfigs.related_objects.append(RobotConfig(
                key=AIConfigType.EMBEDDING_MODEL_NAME,
                value=OpenAIModel.TEXT_EMBEDDING_3_LARGE.value
            ))
        # 构建问题对象
        question_in = QuestionIn(
            session_id=session_id,
            question=question,
            message_id=uuid.uuid4(),
            use_faq=True,
            stream=True,
            with_images=False,
            max_tokens=max_tokens,
        )
        # 构建背景知识信息 - List[DatasetInfo]
        dataset_in_list = [BuildDatasetIn(name=dataset_obj.name, description=dataset_obj.description) for dataset_obj in
                           dataset_objs]
        dataset_infos = await DatasetBaseBuilder.build_knowledge_base(dataset_in_list)
        # 构建上下文对象
        context = AgentContext(
            agent_id=ai_obj.id,
            agent_mode=agent_mode,
        )
        context.set_datasets(dataset_infos)
        context.dataset_ids = dataset_ids
        context.dataset_objs = dataset_objs
        context.robot = ai_obj
        context.question_in = question_in
        context.enable_tool_call_messages=enable_tool_call_messages
        context.runtime_context = runtime_context
        # context.question_record_obj = question_record_obj
        if dataset_ids:
            # 如果有dataset信息, 才注册文档召回工具
            context.register_tools(["traditional_rag_tool"])
        # 转换history为BaseMessage对象
        langchain_messages = convert_to_messages(history)
        logging.info(
            f"【AgentService.chat_agent_for_project_with_traditional_rag】langchain_messages: {langchain_messages}")
        # 调用_run_agent方法
        response = await self._run_agent(
            ai_obj=ai_obj,
            messages=langchain_messages,
            agent_mode=agent_mode,
            question_in=question_in,
            robot=ai_obj,
            base_model=base_model,
            user_id=user_id,
            session_id=session_id,
            datasets=dataset_infos,
            context=context,
            store_message=False,
            event=event,
        )
        logging.info(f"【AgentService.chat_agent_for_project_with_traditional_rag】resp: {response}")
        return response


agent_service = AgentService()


async def debug_summary_service(
        doc_content, doc_title="", doc_type="", doc_tree_path=""
):
    """调试用的包装函数，用于测试agent_summary_service"""
    service = AgentService()
    result = await service.agent_summary_service(
        doc_content=doc_content,
        doc_title=doc_title,
        doc_type=doc_type,
        doc_tree_path=doc_tree_path,
    )
    print(result)


if __name__ == "__main__":
    # from dotenv import load_dotenv, find_dotenv
    # _ = load_dotenv(find_dotenv(filename=".test.env"))  # read local .env file
    article_novel_biography_linchangle = """
    TITLE: 林常乐完整人物小传
    ## 基本信息
    - **全名**：林常乐
    - **年龄**：24岁
    - **职业**：前医学院学生（末日前）
    - **外貌**：长发及肩，大眼睛，皮肤白皙，身材偏瘦但结实
    - **特殊能力**：重生后获得的预知能力，医疗技能
    ## 家庭背景
    - **父亲**：林志远，前军方科学家，在林常乐16岁时因"意外事故"去世，实则与高层秘密实验有关
    - **母亲**：陈雅琳，生物学家，在林常乐20岁时神秘失踪，疑似被高层政府秘密带走
    - **家庭状况**：中产家庭，父母都是科研工作者，童年生活相对稳定但缺乏陪伴
    - **住所**：城市郊区的独栋小屋，有个小花园，是林常乐最后的避风港
    ## 成长经历
    - **童年（0-12岁）**：
      - 父母工作繁忙，常由祖母照顾
      - 展现出超常的观察力和记忆力
      - 8岁时救助了一只受伤的小狗（小黑），成为她童年唯一的伙伴
      - 10岁时参加科学竞赛获奖，父亲开始关注她的潜力
    - **青少年（13-18岁）**：
      - 16岁时父亲意外去世，家庭经济状况恶化
      - 性格开始变得内向、冷漠
      - 凭借优异成绩获得医学院全额奖学金
      - 与顾瑶在高中相识，成为唯一信任的朋友
    - **成年前期（19-24岁）**：
      - 20岁时母亲神秘失踪，林常乐开始独自生活
      - 医学院学习期间，发现一些与父亲研究相关的蛛丝马迹
      - 22岁时因调查母亲下落，接触到高层政府的秘密，被迫中断学业
      - 23岁开始独自调查真相，结识了陆炎和许逸
      - 24岁时末世爆发，在一次救援行动中死亡并重生
    ## 重要事件
    - **父亲去世**：16岁，这一事件彻底改变了她的人生轨迹，也是她性格转变的关键点
    - **母亲失踪**：20岁，促使她开始调查高层政府的秘密
    - **小黑之死**：18岁，唯一的伙伴因病去世，加深了她对医学的执着
    - **医学院退学**：22岁，因接近真相而被迫离开学校，开始地下调查
    - **首次死亡与重生**：24岁，在一次救援同伴的行动中牺牲，随后重生回到灾难前几天
    - **真相揭露**：重生后，逐渐发现父母与高层秘密实验的关联，以及自己体内可能存在的秘密
    ## 性格形成原因
    - **表面冷漠**：父亲去世和母亲失踪的双重打击，让她筑起心墙保护自己
    - **内心柔软**：童年时期祖母的关爱和小黑的陪伴，保留了她内心的温暖
    - **独立自主**：长期独自生活和调查，培养了强大的独立性
    - **重情重义**：失去亲人的痛苦，让她格外珍视仅存的人际关系
    - **理性果断**：医学训练和艰难环境的磨练，塑造了她冷静分析问题的能力
    ## 技能与特长
    - **医疗技能**：接近完成的医学院教育，掌握基础医疗和急救技能
    - **观察分析**：从父亲那里继承的科学思维和敏锐观察力
    - **适应能力**：频繁的环境变化，培养了快速适应新环境的能力
    - **生存技能**：母亲失踪后的独居生活，学会了基本的自保和生存技能
    - **领导才能**：重生后，逐渐展现出组织和领导团队的潜力
    ## 人际关系
    - **与顾瑶**：高中时期唯一的闺蜜，彼此信任，顾瑶是了解真实林常乐的少数人之一
    - **与墨辰**：复杂的吸引与戒备，隐约感觉他与自己的过去有关联
    - **与团队成员**：重生后，逐渐从独行者转变为团队核心，学会信任和依靠他人
    - **与母亲**：深厚的感情，寻找母亲是她重要的动力之一
    - **与父亲**：复杂的情感，既有怀念也有对其秘密研究的疑惑
    ## 内心冲突
    - **信任与戒备**：渴望亲密关系但害怕再次失去
    - **真相与保护**：追求真相可能会伤害到她在意的人
    - **个人与集体**：习惯独自行动，但重生后需要学会团队合作
    - **过去与未来**：纠结于改变过去的悲剧，还是专注于创造新的未来
    - **普通与特殊**：渴望普通生活，却被卷入超出常人的命运
    ## 动机与目标
    - **短期目标**：保护朋友，避免重生前的悲剧重演
    - **中期目标**：揭露高层政府的阴谋，阻止灾难扩大
    - **长期目标**：寻找母亲，解开自身身世之谜
    - **内心渴望**：找到归属感，建立稳定的人际关系
    - **终极追求**：在混乱世界中重建秩序，创造安全的生存环境
    ## 成长轨迹
    - **起点**：重生前的自私自利，只关心自己和少数亲近之人
    - **转折点**：重生后决心改变命运，保护他人
    - **挑战**：面对信任危机、团队冲突和真相的沉重代价
    - **蜕变**：从独行者转变为团队领袖，学会平衡个人与集体利益
    - **最终形态**：兼具理性与感性，能够为更大的目标牺牲个人利益，但不失人性温度
    ## 标志性特点
    - **外在标志**：左手腕上的一道伤疤，是父亲去世那天留下的
    - **习惯动作**：紧张时会无意识地触摸左手腕的伤疤
    - **口头禅**："活着比什么都重要"（重生前）；"活着不只是为了自己"（重生后）
    - **特殊物品**：母亲留下的银色怀表，内藏一张全家福和一串神秘数字
    - **穿着风格**：实用为主，偏爱深色系，总是穿着便于行动的服装
    """
    agent_service = AgentService()
    doc_content = article_novel_biography_linchangle
    doc_title = ""
    doc_type = ""
    doc_tree_path = ""
    resp = asyncio.run(
        debug_summary_service(doc_content, doc_title, doc_type, doc_tree_path)
    )
